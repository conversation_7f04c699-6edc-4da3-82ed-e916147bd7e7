export function randomString(length: number = 5): string {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let count = 0; count < length; count++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
}

    
export function verifyDefined(input?: string, message: string = "Parameter value is required to be other than 'undefined'"): string {
    if (undefined === input) {
        throw new Error(message);
    }
    return input;
}

export const VALUE_NOT_SET = 'The value initially present on the ui should be retained and not to be set by the test.'

export const TRUE_VALUES: Array<string> = ['y', 'yes', 'true', '1', 'on', 'enabled'];
export const FALSE_VALUES: Array<string> = ['n', 'no', 'false', '0', 'off', 'disabled'];