import { TsetButton } from "../../../../components/atoms/TsetButton/TsetButton";
import { BaseObject } from "../../../../components/BaseObject";
import { BaseCell } from "../../../../components/molecules/TsetTable/components/BaseRow";
import { TsetTable } from "../../../../components/molecules/TsetTable/TsetTable";

export class StepDetails extends BaseObject {
  private addToolButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.locator("#table\\.tools").getByTestId("button-add-tool")
  );

  private toolTable: TsetTable = new TsetTable(
    this.page,
    this.config,
    this.page.locator("#table\\.tools").getByTestId("tset-table")
  );

  async openAddToolModal() {
    await this.addToolButton.click();
  }

  async navigateToTool(toolDesignation: string) {
    const toolCell: BaseCell = new BaseCell(
      this.page,
      this.config,
      this.toolTable,
      toolDesignation
    );

    await toolCell.click();
  }
}
