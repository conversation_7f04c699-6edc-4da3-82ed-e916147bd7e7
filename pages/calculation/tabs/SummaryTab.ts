import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { BaseObject } from "../../../components/BaseObject";

export class SummaryTab extends BaseObject {
  private addManufacturingStepButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("empty-breakdown-card-add-step")
  );

  async addManufacturingStep() {
    return await this.addManufacturingStepButton.click();
  }
}
