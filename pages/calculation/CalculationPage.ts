import { BaseObject } from "../../components/BaseObject";
import { test } from "../../fixture";
import { BomExplorer } from "./components/BomExplorer/BomExplorer";
import { CalculationHeader } from "./components/CalculationHeader";
import { NavigationSideBar } from "./components/NavigationSideBar";
import { SummaryTab } from "./tabs/SummaryTab";

export class CalculationPage extends BaseObject {
  private navigationSideBar: NavigationSideBar = new NavigationSideBar(
    this.page,
    this.config,
    this.page.getByTestId("navigation-side-bar")
  );
  private bomExplorer: BomExplorer = new BomExplorer(this.page, this.config);
  calculationHeader: CalculationHeader = new CalculationHeader(
    this.page,
    this.config
  );

  async openAddCalculationModal() {
    await test.step("Open Add Calculation Modal", async () => {
      await this.navigationSideBar.openAddCalculationModal();
    });
  }

  async navigateToBomExplorerNodeByIndex(index: number) {
    await this.bomExplorer.navigateToNodeByIndex(index);
  }

  async navigateToBomExplorerNodeByName(name: string) {
    await this.bomExplorer.navigateToNodeByName(name);
  }
}
