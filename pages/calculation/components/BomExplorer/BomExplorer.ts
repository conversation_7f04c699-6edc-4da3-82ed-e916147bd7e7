import { Locator } from "@playwright/test";
import { BaseObject } from "../../../../components/BaseObject";

export class BomExplorer extends BaseObject {
  private mainLocator: Locator = this.page.getByTestId(
    "nu-bom-exp-scroll-container"
  );

  async navigateToNodeByIndex(index: number) {
    return await this.mainLocator
      .getByTestId("nu-navigation-link")
      .nth(index)
      .click();
  }

  async navigateToNodeByName(name: string) {
    await this.mainLocator.getByRole("link", { name, exact: true }).click();
  }
}
