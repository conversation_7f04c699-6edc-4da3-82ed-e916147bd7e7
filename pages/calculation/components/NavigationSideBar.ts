import { BaseComponent } from "../../../components/BaseComponent";
import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { test } from "../../../fixture";

export class NavigationSideBar extends BaseComponent {
  private addCalculationButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.locator.getByTestId("navigation-side-bar-button-add-calculation")
  );

  public async openAddCalculationModal() {
    await test.step("Open Add Calculation Modal", async () => {
      await this.addCalculationButton.click();
      await this.addCalculationButton.getLocator().isDisabled();
    });
  }
}
