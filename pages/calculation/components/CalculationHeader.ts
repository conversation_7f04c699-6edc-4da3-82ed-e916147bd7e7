import { expect, Locator } from "@playwright/test";
import { BaseObject } from "../../../components/BaseObject";
import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";

export class CalculationHeader extends BaseObject {
  private mainLocator: Locator = this.page.getByTestId("the-manu-header");
  public saveCalculationButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.mainLocator.getByTestId("button-save")
  );
  private deleteCalculationButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.mainLocator.getByTestId("manu-header-delete-calculation-btn")
  );

  async saveCalculation() {
    await expect(this.saveCalculationButton.getLocator()).toBeEnabled();
    await this.saveCalculationButton.click();
  }

  async openDeleteCalculationModal() {
    await expect(this.deleteCalculationButton.getLocator()).toBeEnabled();
    await this.deleteCalculationButton.click();
  }
}
