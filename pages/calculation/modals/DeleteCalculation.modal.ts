import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { BaseObject } from "../../../components/BaseObject";

export class DeleteCalculatioModal extends BaseObject {
  private deleteButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("button-delete")
  );

  async deleteCalculation() {
    return await this.deleteButton.click();
  }
}
