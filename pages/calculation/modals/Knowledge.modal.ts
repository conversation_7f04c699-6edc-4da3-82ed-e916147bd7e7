import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { TsetInput } from "../../../components/atoms/TsetInput/TsetInput";
import { BaseObject } from "../../../components/BaseObject";

/* type stepFields = "displayDesignation" | "partsPerCycle" | "utilizationRate"; */

export class KnowledgeModal extends BaseObject {
  private editManuallyButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("knowledge-tabs-manually")
  );

  private addStepButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("button-add-manufacturing-step")
  );

  private manualStepFields: Record<string, TsetInput> = {
    displayDesignation: new TsetInput(
      this.page,
      this.config,
      this.page
        .getByTestId("field-displayDesignation-null")
        .getByTestId("input-element")
    ),
    partsPerCycle: new TsetInput(
      this.page,
      this.config,
      this.page
        .getByTestId("field-partsPerCycle-null")
        .getByTestId("input-element")
    ),
    utilizationRate: new TsetInput(
      this.page,
      this.config,
      this.page
        .getByTestId("field-utilizationRate-null")
        .getByTestId("input-element")
    ),
  };

  async goToEditManuallyTab() {
    return await this.editManuallyButton.click();
  }

  async addManufacturingStep(args: {
    displayDesignation: string;
    partsPerCycle?: number;
    utilizationRate?: number;
  }) {
    /* for (const key in this.manualStepFields) {
      const field = this.manualStepFields[key as stepFields];
      if (key in args) {
        await field.enterText(args[key as stepFields]);
      }
    } */
    await this.manualStepFields.displayDesignation.enterText(
      args.displayDesignation
    );
    if (args.partsPerCycle != null) {
      await this.manualStepFields.partsPerCycle.enterText(
        args.partsPerCycle.toString()
      );
    }
    if (args.utilizationRate != null) {
      await this.manualStepFields.utilizationRate.enterText(
        args.utilizationRate.toString()
      );
    }
    await this.addStepButton.click();
  }
}
