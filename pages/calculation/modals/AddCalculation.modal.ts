import { TsetButton } from "@/components/atoms/TsetButton/TsetButton";
import { TsetInput } from "@/components/atoms/TsetInput/TsetInput";
import { TsetWave } from "@/components/atoms/TsetWave/TsetWave";
import { BaseObject } from "@/components/BaseObject";
import { NuClassSelector } from "@/components/molecules/NuClassSelector/NuClassSelector";
import { TsetHierarchicalSelect } from "@/components/organisms/TsetHierarchicalSelect/TsetHierarchicalSelect";
import { CalculationAttachment } from "@/domains/calculation/business/attachment";
import { CalculationCreationType } from "@/types/calculation/calculation.data";
import path from "path";

export class AddCalculationModal extends BaseObject {
  private loader: TsetWave = new TsetWave(this.page, this.config);
  private addCalculationButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("wizard-calculation-actions-add-calculation")
  );

  //#region INPUTS
  private partName: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page
      .getByTestId("field-partDesignation-null")
      .getByTestId("input-element")
  );
  private partNumber: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page.getByTestId("field-partNumber-null").getByTestId("input-element")
  );
  private lifetime: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page.getByTestId("field-lifeTime-null").getByTestId("input-element")
  );
  private averageVolumePerYear: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page
      .getByTestId("field-averageUsableProductionVolumePerYear-null")
      .getByTestId("input-element")
  );
  private peakVolumePerYear: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page
      .getByTestId("field-peakUsableProductionVolumePerYear-null")
      .getByTestId("input-element")
  );
  private calculationTitle: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page
      .getByTestId("field-calculationTitle-null")
      .getByTestId("input-element")
  );
  //#endregion INPUTS

  //#region SELECTS
  private location: TsetHierarchicalSelect = new TsetHierarchicalSelect(
    this.page,
    this.config,
    this.page.getByTestId("tset-hierarchical-select")
  );
  //#endregion SELECTS

  private calculationTypeSelector: NuClassSelector = new NuClassSelector(
    this.page,
    this.config,
    this.page.getByTestId("nu-class-selector")
  );

  public async selectCalculationType(calculationType: CalculationCreationType) {
    let calculationLabel = "";
    switch (calculationType) {
      case "WITH_COST_MODULE":
        calculationLabel = "Calculation with calculation module";
        break;
      case "WITHOUT_COST_MODULE":
        calculationLabel = "Calculation without calculation module";
        break;
      case "ROUGH_ESTIMATE":
        calculationLabel = "Rough estimate";
        break;
      case "IMPORT_EXCEL":
        calculationLabel = "Import from Excel";
        break;
      case "IMPORT_TSET":
        calculationLabel = "Import from TSET file";
        break;
      case "IMPORT_CUSTOM":
        calculationLabel = "Import custom file";
        break;
    }
    await this.calculationTypeSelector.selectItem(calculationLabel);
    await this.loader.waitUntilLoaderIsNotPresent();
  }

  /**
   * Add an attachment like an image or a 3d file
   */
  public async addAttachment() {
    const fileName = "basic.3d.stl";
    
    return await CalculationAttachment.create(
      this.page,
      this.page.getByText("Drop file or browse"),
      path.join(__dirname, "resources/files/3d", fileName)
    );
  }

  /*
  public async createCalculation(args: {
    partName: string;
    partNumber?: string;
    lifetime: number;
    averageVolumePerYear: number;
    peakVolumePerYear?: number;
    location: {
      continent: CONTINENT;
      country?: COUNTRY;
    };
  }) {
    await test.step("Fill calculation details", async () => {
      const {
        partName,
        partNumber,
        lifetime,
        averageVolumePerYear,
        peakVolumePerYear,
        location,
      } = args;
      await this.partName.enterText(partName);
      if (partNumber) {
        await this.partNumber.enterText(partNumber);
      }
      await this.lifetime.enterText(lifetime.toString());
      await this.averageVolumePerYear.enterText(
        averageVolumePerYear.toString()
      );
      if (peakVolumePerYear != null) {
        await this.peakVolumePerYear.enterText(peakVolumePerYear.toString());
      }
      await this.location.open();
      if (!location.country) {
        await this.location.selectEntry(location.continent);
      } else {
        await this.location.navigateEntry(location.continent);
        await this.location.selectEntry(location.country);
      }

      await expect(this.addCalculationButton.getLocator()).toBeEnabled();
      await this.addCalculationButton.click();
      await expect(this.addCalculationButton.getLocator()).not.toBeVisible();
    });
  }
    */
}
