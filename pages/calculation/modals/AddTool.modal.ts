import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { TsetInput } from "../../../components/atoms/TsetInput/TsetInput";
import { BaseObject } from "../../../components/BaseObject";

export class AddToolModal extends BaseObject {
  private fields: Record<string, TsetInput> = {
    displayDesignation: new TsetInput(
      this.page,
      this.config,
      this.page
        .getByTestId("field-displayDesignation-null")
        .getByTestId("input-element")
    ),
    serviceLifeInCycles: new TsetInput(
      this.page,
      this.config,
      this.page
        .getByTestId("field-serviceLifeInCycles-null")
        .getByTestId("input-element")
    ),
    investPerTool: new TsetInput(
      this.page,
      this.config,
      this.page
        .getByTestId("field-investPerTool-null")
        .getByTestId("input-element")
    ),
    maintenanceRate: new TsetInput(
      this.page,
      this.config,
      this.page
        .getByTestId("field-maintenanceRate-null")
        .getByTestId("input-element")
    ),
  };

  private addToolButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("knowledge-actions").getByTestId("button-add-tool")
  );

  async addTool(args: {
    displayDesignation: string;
    serviceLifeInCycles: number;
    investPerTool: number;
    maintenanceRate?: number;
  }) {
    await this.fields.displayDesignation.enterText(args.displayDesignation);
    await this.fields.serviceLifeInCycles.enterText(
      args.serviceLifeInCycles.toString()
    );
    await this.fields.investPerTool.enterText(args.investPerTool.toString());
    if (args.maintenanceRate != null) {
      await this.fields.maintenanceRate.enterText(
        args.maintenanceRate.toString()
      );
    }
    await this.addToolButton.click();
  }
}
