import { Page } from "@playwright/test";
import { TsetTestConfig } from "../../../utils/config/TsetTestConfig";
import { BaseObject } from "../../../components/BaseObject";
import { test } from "../../../fixture";
import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { TsetInput } from "../../../components/atoms/TsetInput/TsetInput";

export class WorkspaceModal extends BaseObject {
  private input: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page.getByTestId("input-element")
  );
  private addButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("workspace-modal-action-button")
  );

  public async setWorkspaceName(value: string) {
    await this.input.enterText(value);
  }

  public async clickAddButton() {
    await this.addButton.click();
  }

  public async createWorkspace(value: string) {
    await test.step(`Create new workspace with name "${value}"`, async () => {
      await this.setWorkspaceName(value);
      await this.clickAddButton();
    });
  }
}
