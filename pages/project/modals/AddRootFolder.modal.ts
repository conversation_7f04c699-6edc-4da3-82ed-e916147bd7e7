import { Page } from "@playwright/test";
import { TsetTestConfig } from "../../../utils/config/TsetTestConfig";
import { BaseObject } from "../../../components/BaseObject";
import { TsetInput } from "../../../components/atoms/TsetInput/TsetInput";
import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { test, expect } from "../../../fixture";

export class AddRootFolderModal extends BaseObject {
  private input: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page.getByTestId("input-element")
  );
  private button: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("upsert-folder-modal-button-save")
  );

  public async createFolder(folderName: string) {
    await test.step(`Create folder ${folderName}`, async () => {
      await this.input.enterText(folderName);
      await expect(this.button.getLocator()).toBeEnabled();
      await this.button.click();
      await expect(this.button.getLocator()).not.toBeVisible();
    });
  }
}
