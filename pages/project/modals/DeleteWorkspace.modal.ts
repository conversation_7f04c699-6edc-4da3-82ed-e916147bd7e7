import { Locat<PERSON>, <PERSON> } from "@playwright/test";
import { BaseObject } from "../../../components/BaseObject";
import { TsetTestConfig } from "../../../utils/config/TsetTestConfig";
import { TsetInput } from "../../../components/atoms/TsetInput/TsetInput";
import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { test, expect } from "../../../fixture";

export class DeleteWorkspaceModal extends BaseObject {
  private input: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page.getByTestId("input-element")
  );
  private cancelButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("delete-workspace-modal-cancel-button")
  );
  private confirmButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("delete-workspace-modal-action-button")
  );

  public async cancel() {
    await test.step("Canceling workspace deletion", async () => {
      await this.cancelButton.click();
    });
  }

  public async deleteWorkspace(workspaceName: string) {
    await test.step(`Confirming deletion of workspace ${workspaceName}`, async () => {
      await this.input.enterText(workspaceName);
      await expect(this.confirmButton.getLocator()).toBeEnabled();
      await this.confirmButton.click();
      await expect(this.confirmButton.getLocator()).not.toBeVisible();
    });
  }
}
