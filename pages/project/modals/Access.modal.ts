import { expect, Page } from "@playwright/test";
import { TsetTestConfig } from "../../../utils/config/TsetTestConfig";
import { BaseObject } from "../../../components/BaseObject";
import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { test } from "../../../fixture";

export class AccessModal extends BaseObject {
  private button: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("button-save-changes")
  );

  public async saveChanges() {
    await test.step("Save access changes", async () => {
      await this.button.click();
      await expect(this.button.getLocator()).not.toBeVisible();
    });
  }
}
