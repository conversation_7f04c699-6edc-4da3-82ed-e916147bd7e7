import test, { Page, expect } from "@playwright/test";
import { TsetButton } from "../../../components/atoms/TsetButton/TsetButton";
import { TsetInput } from "../../../components/atoms/TsetInput/TsetInput";
import { TsetTestConfig } from "../../../utils/config/TsetTestConfig";
import { BaseObject } from "../../../components/BaseObject";

export class AddProjectModal extends BaseObject {
  private displayDesignation: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page
      .getByTestId("field-displayDesignation-null")
      .getByTestId("input-element")
  );
  private projectKey: TsetInput = new TsetInput(
    this.page,
    this.config,
    this.page.getByTestId("field-projectKey-null").getByTestId("input-element")
  );
  private button: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.page.getByTestId("add-project-modal-action-button")
  );

  public async createProject(projectName: string, projectKey?: string) {
    await test.step(`Create project ${projectName}`, async () => {
      await this.displayDesignation.enterText(projectName);
      if (projectKey) {
        await this.projectKey.enterText(projectKey);
      }
      await expect(this.button.getLocator()).toBeEnabled();
      await this.button.click();
      await expect(this.button.getLocator()).not.toBeVisible();
    });
  }
}
