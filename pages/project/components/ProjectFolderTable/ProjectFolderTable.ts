import { Page } from "@playwright/test";
import { TsetTestConfig } from "../../../../utils/config/TsetTestConfig";
import { test, expect } from "../../../../fixture";
import { BaseObject } from "../../../../components/BaseObject";
import { TsetTable } from "../../../../components/molecules/TsetTable/TsetTable";
import { TsetButton } from "../../../../components/atoms/TsetButton/TsetButton";

export class ProjectFolderTable extends BaseObject {
  public table: TsetTable = new TsetTable(
    this.page,
    this.config,
    this.page.getByTestId("project-folder-table").getByTestId("tset-table")
  );
  private addWorkspaceButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.table.getTableHeader().getByTestId("add-workspace-button")
  );
  private addRootFolderButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.table.getTableFooter().getByTestId("button-add-root-folder")
  );

  public async clickWorkspaceName(workspaceName: string) {
    await test.step(`Click on ${workspaceName}`, async () => {
      await this.table.getLocator().getByText(workspaceName).click();
    });
  }

  public async clickCreateWorkspaceButton() {
    await test.step('Click on "Create Workspace" button', async () => {
      await this.addWorkspaceButton.click();
    });
  }

  public async clickAddRootFolderButton() {
    await test.step('Click on "Add root folder" button', async () => {
      await this.addRootFolderButton.click();
    });
  }

  public async checkIfWorkspaceIsPresent(workspaceName: string) {
    await test.step(`Checking if workspace ${workspaceName} is present`, async () => {
      await expect(
        this.table.getTableHeader().getByText(workspaceName)
      ).toBeVisible();
    });
  }

  public async checkIfWorkspaceIsNotPresent(workspaceName: string) {
    await test.step(`Checking if workspace ${workspaceName} is not present`, async () => {
      await expect(
        this.table.getTableHeader().getByText(workspaceName)
      ).not.toBeVisible();
    });
  }

  public async openWorkspaceContextMenu(workspaceName: string) {
    await test.step(`Opening workspace ${workspaceName} context menu`, async () => {
      const workspace = this.table.getTableHeader().getByText(workspaceName);
      await workspace.click({ button: "right" });
    });
  }
}
