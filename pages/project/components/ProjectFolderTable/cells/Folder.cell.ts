import { expect, Locator, Page } from "@playwright/test";
import { test } from "../../../../../fixture";
import { BaseCell } from "../../../../../components/molecules/TsetTable/components/BaseRow";

export class FolderCell extends BaseCell {
  public async openAddNewFolder() {
    await test.step('Open "Add Folder" Modal', async () => {
      await this.mainLocator.click({ button: "right" });
      const addProjectItem = this.page.getByTestId("addProject");
      await expect(addProjectItem).toBeEnabled();
      await addProjectItem.click();
      await expect(addProjectItem).not.toBeVisible();
    });
  }
}
