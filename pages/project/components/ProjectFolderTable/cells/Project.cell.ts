import { Locator, <PERSON> } from "@playwright/test";
import { BaseCell } from "../../../../../components/molecules/TsetTable/components/BaseRow";
import { test } from "../../../../../fixture";
import { TsetTable } from "../../../../../components/molecules/TsetTable/TsetTable";
import { TsetTestConfig } from "../../../../../resources/config/utils/TsetTestConfig";

export class ProjectCell extends BaseCell {
  private rowNavigateLink: Locator;

  public constructor(
    page: Page,
    config: TsetTestConfig,
    table: TsetTable,
    rowName: string
  ) {
    super(page, config, table, rowName);
    this.rowNavigateLink = this.mainLocator.getByTestId(
      "name-cell-formatter-text"
    );
  }

  public async navigateToProject() {
    await test.step(`Click on project link`, async () => {
      await this.rowNavigateLink.click();
    });
  }
}
