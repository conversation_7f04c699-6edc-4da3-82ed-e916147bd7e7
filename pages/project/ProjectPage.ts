import { expect, test } from "../../fixture";
import { BaseObject } from "../../components/BaseObject";
import { DeleteWorkspaceModal } from "./modals/DeleteWorkspace.modal";
import { FolderCell } from "./components/ProjectFolderTable/cells/Folder.cell";
import { ProjectFolderTable } from "./components/ProjectFolderTable/ProjectFolderTable";
import { BaseCell } from "../../components/molecules/TsetTable/components/BaseRow";
import { ProjectCell } from "./components/ProjectFolderTable/cells/Project.cell";

export class ProjectPage_OldObject extends BaseObject {
  private projectFolderTable = new ProjectFolderTable(this.page, this.config);

  async openAddWorkspaceModal() {
    await test.step("Open Add Workspace Modal", async () => {
      await this.projectFolderTable.clickCreateWorkspaceButton();
    });
  }

  async checkIfWorkspaceIsPresent(workspaceName: string) {
    return await this.projectFolderTable.checkIfWorkspaceIsPresent(
      workspaceName
    );
  }

  async moveIntoWorkspace(workspaceName: string) {
    return await this.projectFolderTable.clickWorkspaceName(workspaceName);
  }

  async deleteWorkspace(workspaceName: string) {
    await test.step(`Deleting workspace ${workspaceName}`, async () => {
      await this.projectFolderTable.openWorkspaceContextMenu(workspaceName);

      const deleteWorkspaceActionItem = this.page
        .getByTestId("tset-context-menu-actions")
        .getByText("Delete Workspace");
      await deleteWorkspaceActionItem.click();
      const deleteWorkspaceModal = new DeleteWorkspaceModal(
        this.page,
        this.config
      );

      await deleteWorkspaceModal.deleteWorkspace(workspaceName);
      await this.projectFolderTable.checkIfWorkspaceIsNotPresent(workspaceName);
    });
  }

  async openAddRootFolderModal() {
    await test.step('Open "Add Root Folder" Modal', async () => {
      await this.projectFolderTable.clickAddRootFolderButton();
    });
  }

  async openAddProjectToFolderModal(folderName: string) {
    await test.step(`Open "Add Project" modal for folder ${folderName}`, async () => {
      const folderRow = new FolderCell(
        this.page,
        this.config,
        this.projectFolderTable.table,
        folderName
      );
      await folderRow.openAddNewFolder();
    });
  }

  async openProjectFolderNode(folderName: string) {
    await test.step(`Opening folder ${folderName}`, async () => {
      const nodeCell = new FolderCell(
        this.page,
        this.config,
        this.projectFolderTable.table,
        folderName
      );
      await nodeCell.clickOnChevron();
    });
  }

  async navigateToNode(projectName: string) {
    await test.step(`Navigate to node ${projectName}`, async () => {
      const projectRow = new ProjectCell(
        this.page,
        this.config,
        this.projectFolderTable.table,
        projectName
      );
      await projectRow.navigateToProject();
    });
  }
}
