import { Locator, Page } from "@playwright/test";
import { test } from "../../fixture";
import { BaseObject } from "../../components/BaseObject";
import { TsetTestConfig } from "../../utils/config/TsetTestConfig";

export class LoginPage extends BaseObject {
  private usernameInput(): Locator {
    return this.page.locator("#username");
  }

  private passwordInput(): Locator {
    return this.page.locator("#password");
  }

  private loginButton(): Locator {
    return this.page.locator("#kc-login");
  }

  private alert(): Locator {
    return this.page.locator(".kc-feedback-text");
  }

  public async enterCredentials(username: string, password: string) {
    await test.step(`Enter username '${username}' and password on login page`, async () => {
      await this.usernameInput().fill(username);
      await this.passwordInput().fill(password);
      await this.loginButton().click();
    });
  }

  public async isErrorVisisble(): Promise<boolean> {
    return await test.step("Check if an alert is visible on the login page", async () => {
      return await this.alert().isVisible();
    });
  }
}
