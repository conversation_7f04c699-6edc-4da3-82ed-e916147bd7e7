import path from "path";
import dotenv from "dotenv";
import { TRUE_VALUES } from "../../../utils/value"

export class DotEnvDto {
  public static readonly NO_ERROR_FOR_EXPECTED_VALUE: string = "";

  private checkValue(
    value: string | undefined,
    name: string
  ): [string, string] {
    return undefined !== value
      ? [value, DotEnvDto.NO_ERROR_FOR_EXPECTED_VALUE]
      : [
          "",
          `Could not read valid value for ${name}. Please check if it's set correctly in .env or env variables.`,
        ];
  }

  constructor() {
    dotenv.config({ path: path.resolve(__dirname, ".env") });
    let error = [
      this.checkTsetUsername()[1],
      this.checkTsetUserPassword()[1],
      this.checkClientUser()[1],
      this.checkClientSecret()[1],
      this.checkKeycloackUrl()[1],
      this.checkRecordVideo()[1],
    ]
      .filter((message) => message.length > 0)
      .join("\n");
    if (error.length > 0) {
      throw new Error(
        `Some of the mandatory parameters were not set in env\n${error}`
      );
    }
  }

  private checkTsetUsername(): [string, string] {
    return this.checkValue(process.env.TSET_USERNAME, "TSET_USERNAME");
  }
  public getTsetUsername(): string {
    let [result, error] = this.checkTsetUsername();
    if (error.length > 0) {
      throw new Error(error);
    }
    return result;
  }

  private checkTsetUserPassword(): [string, string] {
    return this.checkValue(process.env.TSET_PASSWORD, "TSET_PASSWORD");
  }
  public getTsetUserPassword(): string {
    let [result, error] = this.checkTsetUserPassword();
    if (error.length > 0) {
      throw new Error(error);
    }
    return result;
  }

  private checkClientUser(): [string, string] {
    return this.checkValue(
      process.env.KEYCLOAK_CLIENT_USER,
      "KEYCLOAK_CLIENT_USER"
    );
  }
  public getClientUser(): string {
    let [result, error] = this.checkClientUser();
    if (error.length > 0) {
      throw new Error(error);
    }
    return result;
  }

  private checkClientSecret(): [string, string] {
    return this.checkValue(
      process.env.KEYCLOAK_CLIENT_SECRET,
      "KEYCLOAK_CLIENT_SECRET"
    );
  }
  public getClientSecret(): string {
    let [result, error] = this.checkClientSecret();
    if (error.length > 0) {
      throw new Error(error);
    }
    return result;
  }

  private checkKeycloackUrl(): [string, string] {
    return this.checkValue(process.env.KEYCLOACK_URL, "KEYCLOACK_URL");
  }
  public getKeycloackUrl(): string {
    let [result, error] = this.checkKeycloackUrl();
    if (error.length > 0) {
      throw new Error(error);
    }
    return result;
  }

  private checkRecordVideo(): [string, string] {
    if (undefined === process.env.VIDEO_RECORDING_FOR_LOCAL_RUN) {
      return ["false", DotEnvDto.NO_ERROR_FOR_EXPECTED_VALUE];
    }
    return this.checkValue(
      process.env.KEYCLOACK_URL,
      "VIDEO_RECORDING_FOR_LOCAL_RUN"
    );
  }
  public getRecordVideo(): boolean {
    let [result, error] = this.checkRecordVideo();
    if (error.length > 0) {
      throw new Error(error);
    }
    return TRUE_VALUES.includes(result);
  }

  private checkSelectOptionTimeoutOverride(): [string, string] {
    if (undefined === process.env.SELECT_OPTION_TIMEOUT_OVERRIDE) {
      return ["", DotEnvDto.NO_ERROR_FOR_EXPECTED_VALUE];
    }
    return this.checkValue(
      process.env.SELECT_OPTION_TIMEOUT_OVERRIDE,
      "SELECT_OPTION_TIMEOUT_OVERRIDE"
    );
  }
  public getSelectOptionTimeoutOverride(): string {
    let [result, error] = this.checkSelectOptionTimeoutOverride();
    if (error.length > 0) {
      throw new Error(error);
    }
    return result;
  }
}
