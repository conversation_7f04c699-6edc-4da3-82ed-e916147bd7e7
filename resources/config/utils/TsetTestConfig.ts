import { Users } from "./users";
import { User } from "./user";
import { FullConfig } from "@playwright/test";
import { DotEnvDto } from "./DotEnvDto"

export class TsetTestConfig {
  public readonly env: DotEnvDto;
  public readonly responseBodyLengthLimit: number = 102400;
  public readonly users: Users = Users.readUsers();
  public readonly playwrightConfig: FullConfig;

  constructor(playwrightConfig: FullConfig) {
    // TODO: Experiement with changing this config for a test after the fixture has been created.
    this.playwrightConfig = playwrightConfig;
    this.env = new DotEnvDto();
  }

  public getCurrentUser(): User {
    return this.users.getUser(this.env.getTsetUsername());
  }

  private getActionTimeoutFromPlaywrightConfig(): number {
    let result = this.playwrightConfig.projects.at(0)?.use.actionTimeout;
    if (undefined === result) {
      throw new Error("actionTimeout is not set in playwright configuration.");
    } else {
      return result;
    }
  }

  public getSelectOptionTimeout(): number {
    let ovverideValue = this.env.getSelectOptionTimeoutOverride();
    if (ovverideValue.length > 0) {
      return Number.parseInt(ovverideValue);
    }
    return this.getActionTimeoutFromPlaywrightConfig();
  }
}
