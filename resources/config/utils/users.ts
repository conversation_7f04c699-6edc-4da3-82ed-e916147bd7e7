import path from "path";
import fs from "fs";
import * as allure from "allure-js-commons";
import { User } from "./user";

export class Users {
    private users: Array<User> = [];
    private static usersPath = path.join(__dirname, `../../../playwright/.auth/users`); 

    public getUser(name: String): User {
        let result = this.users.find(user => user.getName() == name);
        if (undefined === result) {
            throw new Error(`Could not find user: '${name}'`)
        }
        return result;
    }

    public static writeUser(name: string, password?: string, accessToken?: string) {
        fs.mkdirSync(Users.usersPath, { recursive: true });
        fs.writeFileSync(
            path.join(Users.usersPath, `${name}.json`),
            JSON.stringify(new User(name, password, accessToken)));
    }

    public static readUsers(): Users {
        if (!fs.existsSync(Users.usersPath)) {
            allure.attachment('No users found', `File not found at path: ${Users.usersPath}\nUser configuration has to be takend from .env`, allure.ContentType.TEXT);
            return new Users();
        }

        let files = fs.readdirSync(Users.usersPath);
        allure.attachment('Users found', JSON.stringify(files), allure.ContentType.TEXT);

        let result: Users = new Users();

        // allure.attachment('file content', fs.readFileSync(path.join(Users.usersPath, undefined === files.at(0))), allure.ContentType.TEXT);

        files.map(file => JSON.parse(fs.readFileSync(path.join(Users.usersPath, file), 'utf-8')))
            .map(obj => new User(obj["name"], obj["password"], obj["accessToken"]))
            .forEach(user => {
                result.users.push(user);
            });

        allure.attachment('User configuration created', JSON.stringify(result), allure.ContentType.TEXT);

        return result;
    }
}