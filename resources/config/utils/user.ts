import { TsetPreferences } from "./TsetPreferences";

export class User {
    private name: string;
    private password?: string;
    private accessToken?: string;
    // TODO: Read preferences by listening to api
    private preferences?: TsetPreferences = new TsetPreferences();

    constructor(name: string, password?: string, accessToken?: string) {
        this.name = name;
        this.password = password;
        this.accessToken = accessToken
    }

    public getName(): string {
        return this.name;
    }

    public getPassword(): string {
        if (undefined === this.password) {
            throw new Error(`Password not set for user '${this.name}'`);
        }

        return this.password;
    }

    public setPassword(password: string): void {
        this.password = password;
    }

    public getAccessToken(): string {
        if (undefined === this.accessToken) {
            throw new Error(`Access token not set for user '${this.name}'`);
        }

        return this.accessToken;
    }

    public setAccessToken(accessToken: string): void {
        this.accessToken = accessToken;
    }

    public getPreferences(): TsetPreferences {
        if (undefined === this.preferences) {
            return new TsetPreferences();
        }

        return this.preferences;
    }
}