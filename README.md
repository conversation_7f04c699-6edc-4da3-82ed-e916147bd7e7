# playwright-test

## Requirements

- `Node @ >=20`
- `pnpm` package manager

> **Note:** Don't use `devbox`. This will lead to errors due to lack of access to global libraries (E.g. `glib`)

## Enviroment variables

> **Note:** In different Linux distributions seems not to work exactly the same. For this case, is better to manually update the variables inside the **.env** file for both the `ENV_NAME` and the `KEYCLOACK_URL` variables (KEYCLOACK_URL has a template explaining what to replace)

```
export ENV=testing
export ENV_NAME=testing
```

## Scripts to populate permissions

Similar to the old nu-ui-test suite, it's needed to get the values for the keys and passwords from the 1password vaults

### Give the files permissions (If needed)

```
chmod +x ./scripts/collect.config.sh
chmod +x ./scripts/write.env.sh
```

### Log into 1password from the terminal

```
op signin -f
```

A window for 1password will open. Log into 1password using your usual password.
You might get a notification to run an eval script in the termina. DO THAT!

### Run the scripts

This scripts will log into the vault from 1password and grab all the needed access keys to the relevant testing repos. This will be written into the local `.env` file, which must remain on the `.gitignore` file

```
./scripts/collect.config.sh
./scripts/write.env.sh
```

> **Note:** If the values need to be rewritten for any reason, first go onto the `.env` file and delete all the variables present except for `ENV_NAME` and `KEYCLOACK_URL`. This scripts do not update previously created variables.

## Run the tests

There are 4 commands to run the tests:

### To get the access to the API calls run:

```
pnpm run authapi
```

> **Note:** This command needs to be run only once per user that wants to use the test suite. 9/10 is going to be `antman`, so once will be needed. **Run this command once before starting to use the rest**.

### To do the setup steps for the tests

```
pnpm run setuptest
```

E.g: Login into the tests, which should be done only once and will allow that a whole suite has the same token.

### Smoke Tests

```
pnpm run smoke
```

### Regression Tests

```
pnpm run test
```
