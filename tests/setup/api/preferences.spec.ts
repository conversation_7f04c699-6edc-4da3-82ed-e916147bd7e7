import { getRequest } from "../../../api/utils/RequestMethods";
import { test, expect } from "../../../fixture";

test("Read user preferences", async ({ request, config }) => {
  const auth = await getRequest(
    "Call GET api/user/preferences",
    request,
    config,
    "api/user/preferences",
    {
      headers: {
        Authorization: `Bearer ${config.getCurrentUser().getAccessToken()}`,
      },
    }
  );

  expect(auth.ok()).toBeTruthy();
});
