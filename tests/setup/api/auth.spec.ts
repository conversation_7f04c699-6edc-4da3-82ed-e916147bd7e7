import { postRequest } from "../../../api/utils/RequestMethods";
import { test, expect } from "../../../fixture";
import * as allure from "allure-js-commons";
import { Users } from "../../../resources/config/utils/users";

test("Call token endpoint", async ({ request, config }) => {
  let username: string = config.env.getTsetUsername();
  let password: string = config.env.getTsetUserPassword();
  let client_id: string = config.env.getClientUser();
  let client_secret: string = config.env.getClientSecret();

  await allure.attachment(
    "Config",
    JSON.stringify(config),
    allure.ContentType.JSON
  );

  let options: Object = {
    form: {
      client_id: client_id,
      grant_type: "password",
      client_secret: client_secret,
      scope: "openid",
      username: username,
      password: password,
    },
  };

  const auth = await postRequest(
    "Call Authorization",
    request,
    config,
    config.env.getKeycloackUrl(),
    options
  );

  expect(auth.ok()).toBeTruthy();
  Users.writeUser(username, password, (await auth.json()).access_token);
});
