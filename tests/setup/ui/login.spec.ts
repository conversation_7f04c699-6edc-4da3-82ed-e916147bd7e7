import { expect, test } from "@/fixtures/setup/ui/fixture";
import path from "path";

let browserSessionPath = path.join(
  __dirname,
  `../../../playwright/.auth/session/browser`
);

test("Login to tset", async ({ loginPage, config, page }) => {
  await page.goto("/");

  await expect(page).toHaveTitle("Sign in to tset-platform-testing");
  await loginPage.enterCredentials(
    config.getCurrentUser().getName(),
    config.getCurrentUser().getPassword()
  );

  await expect(page.getByTestId("tset-logo")).toBeVisible();

  await page.context().storageState({
    path: path.join(
      browserSessionPath,
      `${config.getCurrentUser().getName()}.json`
    ),
  });
});
