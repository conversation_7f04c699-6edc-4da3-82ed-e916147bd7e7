import { testEmptyProject as test } from "@/fixtures/functional/project/emptyProject.fixture";
import { expect } from "@playwright/test";

test("Hoops viewer should work in add calculation modal and part tab", async ({
  page,
  project,
}) => {
  // Go to project page
  const projectPage =
    await test.step(`Go to project: '${project.name}'`, async () => {
      return await project.goto();
    });

  // Start add calculation
  const addCalculationModal =
    await test.step(`Open add calculation modal`, async () => {
      return await projectPage.startToAddManualCalculation();
    });

  const attachment =
    await test.step(`Fill the add calculation form`, async () => {
      return await addCalculationModal.addAttachment();
    });

  await test.step(`Open 3D attachment file`, async () => {
    await attachment.open();
  });

  const hoopsViewer = await page.getByTestId("hoops-3-d-viewer");

  await expect(hoopsViewer).toBeVisible({ timeout: 3000 });
  await hoopsViewer.screenshot({
    path: "hoops-add-calculation-attachment.png",
  });
  // check for the content of hoops
});
