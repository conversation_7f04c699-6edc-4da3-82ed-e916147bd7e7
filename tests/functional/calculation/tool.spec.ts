import { testChillCalculation as test } from "@/fixtures/functional/calculation/chillCalculation.fixture";
import { expect } from "@playwright/test";

test("Tool page should have the correct fields", async ({
  page,
  calculation,
}, testinfo) => {
  const tool = calculation.tools?.at(0);
  if (!tool) {
    throw new Error("Tool is not defined");
  }

  await test.step(`Go to detail page for tool ${tool.name}`, async () => {
    await tool.goTo();
  });

  // REFACTOR END
  // The existing tool detail page test from here on

  await expect
    .soft(page.getByTestId("detail-page").getByText("Investment"))
    .toBeVisible();
  await expect
    .soft(page.getByTestId("detail-page").getByText("Allocated costs"))
    .toBeVisible();
  await expect
    .soft(page.getByTestId("detail-page").getByText("Maintenance costs"))
    .toBeVisible();

  // Check that the fields are present
  // Investment Card
  await expect
    .soft(
      page.locator(
        '[data-test="field-numberOfConcurrentlyUsedToolsPerSystem-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-investPerTool-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-serviceLifeInCycles-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-conceptCost-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-cyclesOverLifeTime-TOOL"] [data-test="readonly-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-systemCount-TOOL"] [data-test="readonly-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-baseCurrency-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-quantity-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-proportionalInvest-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-invest-TOOL"] [data-test="readonly-layout-label"]'
      )
    )
    .toBeVisible();

  // Allocated Costs Card
  await expect
    .soft(
      page.locator(
        '[data-test="field-allocatedInvestTotal-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-toolAllocationMode-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-investCost-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-allocatedToolQuantity-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-interestRate-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-allocationRatio-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-interestCalcPeriod-TOOL"] [data-test="readonly-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-investAndInterestAllocationVolume-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();

  // Maintenance Costs Card
  await expect
    .soft(
      page.locator(
        '[data-test="field-investWithoutConceptCost-TOOL"] [data-test="readonly-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-toolMaintenanceType-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();
  await expect
    .soft(
      page.locator(
        '[data-test="field-maintenanceAllocationVolume-TOOL"] [data-test="editable-layout-label"]'
      )
    )
    .toBeVisible();

  // Check that the table is present
  await expect.soft(page.getByRole("grid")).toBeVisible();
});
