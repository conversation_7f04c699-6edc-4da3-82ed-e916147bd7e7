stages:
  - test
  - reporting

image:
  name: mcr.microsoft.com/devcontainers/typescript-node

variables:
  ENV_NAME:
    value: "testing"
  KEYCLOACK_ENV:
    value: "testing"
    options:
      - "testing"
      - "develop"
  TSET_USERNAME: op://Testing/QA.$KEYCLOACK_ENV/username
  TSET_PASSWORD: op://Testing/QA.$KEYCLOACK_ENV/password
  BCT_USERNAME: op://Testing/QA.TSET.$KEYCLOACK_ENV/username
  BCT_PASSWORD: op://Testing/QA.TSET.$KEYCLOACK_ENV/password
  MD_USERNAME: op://Testing/md_service_normal_user.$KEYCLOACK_ENV/username
  MD_PASSWORD: op://Testing/md_service_normal_user.$KEYCLOACK_ENV/password
  MD_SUPER_USERNAME: op://Testing/md_service_superuser.$KEYCLOACK_ENV/username
  MD_SUPER_PASSWORD: op://Testing/md_service_superuser.$KEYCLOACK_ENV/password
  MD_ADMIN_USERNAME: op://Testing/md_service_admin.$KEYCLOACK_ENV/username
  MD_ADMIN_PASSWORD: op://Testing/md_service_admin.$KEYCLOACK_ENV/password
  MD_EDITOR_USERNAME: op://Testing/md_service_masterdata_editor.$KEYCLOACK_ENV/username
  MD_EDITOR_PASSWORD: op://Testing/md_service_masterdata_editor.$KEYCLOACK_ENV/password
  MD_DATA_ADMIN_USERNAME: op://Testing/md_service_masterdata_admin.$KEYCLOACK_ENV/username
  MD_DATA_ADMIN_PASSWORD: op://Testing/md_service_masterdata_admin.$KEYCLOACK_ENV/password
  ROLE_USER_USERNAME: op://Testing/roleuser.$KEYCLOACK_ENV/username
  ROLE_USER_PASSWORD: op://Testing/roleuser.$KEYCLOACK_ENV/password
  ROLE_ADMIN_USERNAME: op://Testing/roleadmin.$KEYCLOACK_ENV/username
  ROLE_ADMIN_PASSWORD: op://Testing/roleadmin.$KEYCLOACK_ENV/password
  ROLE_GROUPLESS_USERNAME: op://Testing/grouplessroleuser.$KEYCLOACK_ENV/username
  ROLE_GROUPLESS_PASSWORD: op://Testing/grouplessroleuser.$KEYCLOACK_ENV/password
  ATTRIBUTE_USER_USERNAME: op://Testing/attributelessuser.$KEYCLOACK_ENV/username
  ATTRIBUTE_USER_PASSWORD: op://Testing/attributelessuser.$KEYCLOACK_ENV/password
  COM_ENGINE_USERNAME: op://Testing/commercial-engine.$KEYCLOACK_ENV/username
  COM_ENGINE_PASSWORD: op://Testing/commercial-engine.$KEYCLOACK_ENV/password
  KEYCLOAK_CLIENT_USER: op://Testing/keycloak.client.qa.$KEYCLOACK_ENV/username
  KEYCLOAK_CLIENT_SECRET: op://Testing/keycloak.client.qa.$KEYCLOACK_ENV/password

tests:
  stage: test
  before_script:
    - corepack prepare pnpm@latest-10 --activate
    - pnpm config set store-dir .pnpm-store
  variables:
    KEYCLOACK_URL: "https://id.$KEYCLOACK_ENV.tset.cloud/realms/tset-platform-$KEYCLOACK_ENV/protocol/openid-connect/token"
    SELECT_OPTION_TIMEOUT_OVERRIDED: 1000
  script:
    # Variable setups
    - echo $ENV
    - echo $ENV_NAME
    - echo $KEYCLOACK_URL
    # Package setups
    - pnpm install
    - pnpm exec playwright install --with-deps
    - pnpm run test
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  rules:
    - if: $ENV =~ /^[a-zA-Z0-9-]+$/
      variables:
        ENV_NAME: "$ENV"
    - when: always
  artifacts:
    when: always
    paths:
      - playwright-report/index.html
  tags:
    - k8s-ui-test

include:
  - project: devops/devenvs/pipeline-base
    ref: main
    file:
      - security/gitlab-ci.yml
      - workflow.yml

slack-reporting:
  stage: reporting
  variables:
    SLACK_WEBHOOK_URL: "*******************************************************************************"
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: always
  script:
    - mkdir public
    - mv playwright-report/* public/
    - npm i --save-dev @types/node
    - tsc scripts/slack/sendSlackReport.ts
    - node --inspect scripts/slack/sendSlackReport.js
  tags:
    - k8s-ui-test

detect.leaks:
  extends: .detect:leaks
