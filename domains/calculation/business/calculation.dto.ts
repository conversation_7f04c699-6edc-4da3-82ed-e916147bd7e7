import { Maybe } from "@/types/maybe";

interface Part {
  designation: string;
  number: string;
  designationNormalized?: string;
  id: string;
  images?: string[];
  createdDate?: Maybe<Date>;
  lastModifiedDate?: Maybe<Date>;
}

type ManufacturingEntityType =
  | "PROCESS"
  | "MANUFACTURING"
  | "MANUFACTURING_STEP"
  | "COMPONENT_MATERIAL"
  | "CYCLETIME_STEP"
  | "CYCLETIME_STEP_GROUP"
  | "MACHINE"
  | "LABOR"
  | "TOOL"
  | "SETUP"
  | "WAGE"
  | "MATERIAL"
  | "MATERIALS"
  | "PROCESSED_MATERIAL"
  | "CONSUMABLE"
  | "C_PART"
  | "LOCATION"
  | "PART"
  | "EXCHANGE_RATES"
  | "EXCHANGE_RATE"
  | "ATTACHMENT"
  | "TTYPE"
  | "TTYPE2"
  | "BOM_ENTRY"
  | "SYSTEM_PARAMETER"
  | "BASE"
  | "CO2_PROCESSING_MATERIAL"
  | "NONE"
  | "PART_TARGET"
  | "ROUGH_CALCULATION"
  | "MANUAL_CALCULATION"
  | "DETAILED_CALCULATION"
  | "TEMPLATE"
  | "MANUFACTURING_STEP_TEMPLATE"
  | "MANUFACTURING_STEP_CONFIGURATION"
  | "RAW_MATERIAL_MANUAL"
  | "ELECTRONIC_COMPONENT"
  | "SPECIAL_DIRECT_COST"
  | "METHOD_PLAN_STAGE"
  | "METHOD_PLAN_FEATURE"
  | "TOOL_COST_STRUCTURE"
  | "TOOL_COST_ROW"
  | "MATERIAL_GEOMETRY"
  | "standardCalculation" /* @todo investigate "standardCalculation" and move to proper place  */
  | "TSET_FILE_IMPORT"
  | "MD_EXCHANGERATE_PARENT"
  | "MD_EXCHANGERATE"
  | "MD_COSTFACTORS_PARENT"
  | "MD_PERIOD"
  | "MD_OVERHEAD"
  | "OVERHEAD_SUB_CALCULATOR"
  | "TRANSPORT_ROUTE"
  | "DETAILED_TOOL_MAINTENANCE_CALCULATOR"
  | "PRICE_COMPONENT";

interface ModelListEntry {
  name: string;
  path: string;
  entity: string;
  displayState: unknown;
  hasShape: boolean;
  cardNames: string[];
}

interface ResultField {
  name: string;
  type: string;
  source: Maybe<"I" | "C" | "M" | "R" | "O">;
  value: unknown;
  empty?: boolean;
  label?: Maybe<string>;
  systemValue?: unknown;
}

export interface ManufacturingDTO {
  id: string;
  children: ManufacturingDTO[];
  className: string;
  copyable: boolean;
  deletable: boolean;
  fields: ResultField[];
  key?: string;
  isolated: boolean;
  latestMasterDataVersion?: number;
  model?: ModelListEntry;
  name: string;
  parentId?: ManufacturingDTO["id"];
  part?: Maybe<Part>;
  ref: string;
  type: ManufacturingEntityType;
  usedMasterDataVersion?: number;
  version: number;
  customProcurementType?: string;
}
