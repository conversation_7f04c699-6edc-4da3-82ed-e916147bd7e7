import { Locator, <PERSON> } from "@playwright/test";

export class CalculationAttachment {
  constructor(private page: Page, private locator: Locator, private fileName: string) {}

  public async open() {
    await this.locator.getByTestId('attachment-image-wrapper').click();
  }

  public static async create(page: Page, fileInput: Locator, filePath: string) {
    const fileName = filePath.split("/").pop();
    if (!fileName) {
      throw new Error(`Failed to get file name from path ${filePath}`);
    }

    const fileChooserPromise = page.waitForEvent("filechooser");
    await fileInput.click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(fileName);

    const box = await page.getByTestId("box").filter({ has: page.getByText(fileName) });

    return new CalculationAttachment(page, box, fileName);
  }
}
