import {
  createCalculationCHILLViaApi,
  deleteCalculationViaApi,
  getDetailedCalculationViaApi,
} from "@/api/public-api/utils/calculation.utils";
import { Folder } from "@/domains/project/business/folder";
import { Project } from "@/domains/project/business/project";
import { Workspace } from "@/domains/project/business/workspace";
import { TsetTestConfig } from "@/resources/config/utils/TsetTestConfig";
import { APIRequestContext, Page } from "@playwright/test";
import { ManufacturingDTO } from "./calculation.dto";
import { Tool } from "./nodes/tool";
import { ManufacturingNode } from "./nodes/node";
import { Material } from "./nodes/material";
import { ManufacturingStep } from "./nodes/manufacturingStep";
import { recursiveGenerateNodeMap } from "./utils";

type CostModule = "ManufacturingChillCasting";

export type CalculationCreationResponseDto = {
  id: string;
  branchId: string;
};

export interface Calculation extends CalculationCreationResponseDto {}
export class Calculation implements CalculationCreationResponseDto {
  private _manufacturing?: ManufacturingDTO = undefined;

  private _nodeMap?: Record<ManufacturingDTO["type"], ManufacturingDTO[]> =
    undefined;

  private _tools?: Tool[] = undefined;
  private _materials?: Material[] = undefined;
  private _manufacturingSteps?: ManufacturingStep[] = undefined;

  constructor(
    private request: APIRequestContext,
    private config: TsetTestConfig,
    private page: Page,
    private _workspace: Workspace,
    private _folder: Folder,
    private _project: Project,
    calculation: CalculationCreationResponseDto
  ) {
    this.id = calculation.id;
    this.branchId = calculation.branchId;
  }

  public get workspace() {
    return this._workspace;
  }

  public get folder() {
    return this._folder;
  }

  public get project() {
    return this._project;
  }

  public get tools() {
    if (!this._manufacturing) {
      throw new Error("Manufacturing not loaded");
    }
    return this._tools;
  }

  public get materials() {
    if (!this._manufacturing) {
      throw new Error("Manufacturing not loaded");
    }
    return this._materials;
  }

  public get manufacturingSteps() {
    if (!this._manufacturing) {
      throw new Error("Manufacturing not loaded");
    }
    return this._manufacturingSteps;
  }

  /**
   * Create a new Calculation.
   * You can specify a workspace, folder and project.
   * If not specified, a new workspace, folder and project will be created.
   *
   * @param request
   * @param config
   * @param args
   * @returns
   */
  public static async create(
    request: APIRequestContext,
    config: TsetTestConfig,
    page: Page,
    args?: {
      costModule?: CostModule;
      workspace?: Workspace;
      folder?: Folder;
      project?: Project;
    }
  ) {
    const workspace =
      args?.workspace ||
      (await Workspace.create(request, config, {
        name: `workspace-${Date.now()}`,
      }));

    const folder =
      args?.folder || (await workspace.addFolder(`folder-${Date.now()}`));

    const projectDate = Date.now();
    const project =
      args?.project ||
      (await folder.addProject(
        `project-${projectDate}`,
        `projectKey-${projectDate}`
      ));

    const calculationDate = Date.now();

    let calculationResponse: CalculationCreationResponseDto | undefined =
      undefined;

    switch (args?.costModule) {
      default:
        calculationResponse = await createCalculationCHILLViaApi(
          request,
          config,
          {
            projectKey: project.key,
            partNumber: calculationDate.toString(),
            partName: `calculation-${calculationDate}`,
          }
        );
    }

    if (!calculationResponse) {
      throw new Error("Calculation response is undefined");
    }

    const calculation = new Calculation(
      request,
      config,
      page,
      workspace,
      folder,
      project,
      calculationResponse
    );
    await calculation.load();
    return calculation;
  }

  public async remove() {
    await deleteCalculationViaApi(
      this.request,
      this.config,
      this.project.key,
      this.id
    );
  }

  /**
   * Load all the details of the calulcation
   */
  public async load() {
    const { manufacturing } = await getDetailedCalculationViaApi(
      this.request,
      this.config,
      this.id,
      this.branchId
    );
    this._manufacturing = manufacturing;

    this._nodeMap = recursiveGenerateNodeMap(manufacturing);

    // build tools
    this._tools = this._nodeMap?.["TOOL"]?.map((dto) => new Tool(this, dto));

    // build materials
    this._materials = [
      ...(this._nodeMap?.["MATERIAL"] || []),
      ...(this._nodeMap?.["CONSUMABLE"] || []),
      ...(this._nodeMap?.["ELECTRONIC_COMPONENT"] || []),
      ...(this._nodeMap?.["C_PART"] || []),
    ].flatMap((dto) => new Material(this, dto));

    // build manufacturing steps
    if (this._nodeMap?.["MANUFACTURING_STEP"]?.length) {
      this._manufacturingSteps = this._nodeMap?.["MANUFACTURING_STEP"]?.map(
        (dto) => new ManufacturingStep(this, dto)
      );
    }
  }

  /**
   * Generate URL for a manufacturing node based on its type and properties
   */
  public generateNodeUrl(node: ManufacturingNode, parentStep?: ManufacturingNode): string {
    const projectKey = this.project.key;
    const baseParams = {
      path: this.id,
      branch: this.branchId
    };

    switch (node.type) {
      case "TOOL":
        return `/project/${projectKey}/manu/detail/${node.id}?${new URLSearchParams({
          ...baseParams,
          ccy: "EUR"
        }).toString()}`;

      case "MATERIAL":
        return `/project/${projectKey}/manu/material/materials?${new URLSearchParams({
          ...baseParams,
          objectId: node.id
        }).toString()}`;

      case "CONSUMABLE":
        return `/project/${projectKey}/manu/material/consumable?${new URLSearchParams({
          ...baseParams,
          objectId: node.id
        }).toString()}`;

      case "MANUFACTURING_STEP":
        return `/project/${projectKey}/manu/step?${new URLSearchParams({
          ...baseParams,
          objectId: node.id,
          step: node.id
        }).toString()}`;

      case "MACHINE":
        if (!parentStep) {
          throw new Error("Parent step is required for machine URL generation");
        }
        return `/project/${projectKey}/manu/step/machine?${new URLSearchParams({
          ...baseParams,
          objectId: node.id,
          step: parentStep.id
        }).toString()}`;

      default:
        // Fallback for other node types
        return `/project/${projectKey}/manu/detail/${node.id}?${new URLSearchParams(baseParams).toString()}`;
    }
  }

  public async goTo(node: ManufacturingNode, parentStep?: ManufacturingNode) {
    await this.page.goto(this.generateNodeUrl(node, parentStep));
  }
}
