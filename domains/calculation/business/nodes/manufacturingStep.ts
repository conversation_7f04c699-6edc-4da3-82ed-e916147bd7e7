import { Calculation } from "../calculation";
import { ManufacturingDTO } from "../calculation.dto";
import { recursiveGenerateNodeMap } from "../utils";
import { Machine } from "./machine";
import { ManufacturingNode } from "./node";

export interface ManufacturingStep extends ManufacturingDTO {}
export class ManufacturingStep implements ManufacturingNode {
  private _machines: Machine[] = [];

  constructor(private calculation: Calculation, dto: ManufacturingDTO) {
    Object.assign(this, dto);

    const childNodes = recursiveGenerateNodeMap(this);
    this._machines = childNodes["MACHINE"].map(
      (dto) => new Machine(this.calculation, dto, this)
    );
  }

  get machines() {
    return this._machines;
  }

  goTo() {
    return this.calculation.goTo(this);
  }
}
