import { Calculation } from "../calculation";
import { ManufacturingDTO } from "../calculation.dto";
import { ManufacturingStep } from "./manufacturingStep";
import { ManufacturingNode } from "./node";

export interface Machine extends ManufacturingDTO {}
export class Machine implements ManufacturingNode {
  private parentStep: ManufacturingStep;

  constructor(
    private calculation: Calculation,
    dto: ManufacturingDTO,
    parentStep: ManufacturingStep
  ) {
    Object.assign(this, dto);
    this.parentStep = parentStep;
  }

  goTo(): Promise<void> {
    return this.calculation.goTo(this, this.parentStep);
  }
}
