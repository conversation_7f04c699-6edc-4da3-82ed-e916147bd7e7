import { Calculation } from "../calculation";
import { ManufacturingDTO } from "../calculation.dto";
import { ManufacturingNode } from "./node";

export interface Material extends ManufacturingDTO {}
export class Material implements ManufacturingNode {
  constructor(private calculation: Calculation, dto: ManufacturingDTO) {
    Object.assign(this, dto);
  }

  goTo() {
    return this.calculation.goTo(this);
  }
}
