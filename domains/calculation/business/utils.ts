import { ManufacturingDTO } from "./calculation.dto";

export function recursiveGenerateNodeMap(
  manufacturing: ManufacturingDTO
): Record<ManufacturingDTO["type"], ManufacturingDTO[]> {
  const nodeMap: Record<string, ManufacturingDTO[]> = {};

  nodeMap[manufacturing.type] = [
    ...(nodeMap?.[manufacturing.type] ?? []),
    manufacturing,
  ];

  manufacturing.children.forEach((node) => {
    nodeMap[node.type] = [...(nodeMap?.[node.type] ?? []), node];
    if (node.children) {
      const childMap = recursiveGenerateNodeMap(node);
      Object.entries(childMap).forEach(([key, value]) => {
        nodeMap[key] = [...(nodeMap?.[key] ?? []), ...value];
      });
    }
  });

  return nodeMap;
}
