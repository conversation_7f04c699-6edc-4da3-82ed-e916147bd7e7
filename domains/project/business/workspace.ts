import {
  createWorkspaceViaAP<PERSON>,
  deleteWorkspaceViaApi,
} from "@/api/public-api/utils/workspace.utils";
import { APIRequestContext } from "@playwright/test";
import { TsetTestConfig } from "@/resources/config/utils/TsetTestConfig";
import { WorkspaceBuilder } from "@/api/public-api/builder/workspace.builder";
import { Folder } from "./folder";

let workspaces: {name: string, workspaceId: string}[] | undefined = undefined;

export type WorkspaceCreateResponseDto = {
  name: string;
  id: string;
};

export interface Workspace extends WorkspaceCreateResponseDto {}
export class Workspace implements WorkspaceCreateResponseDto {
  constructor(
    private request: APIRequestContext,
    private config: TsetTestConfig,
    data: WorkspaceCreateResponseDto
  ) {
    this.id = data.id;
    this.name = data.name;
  }

  public async addFolder(folderName: string) {
    return Folder.create(this.request, this.config, {
      name: folderName,
      workspaceId: this.id,
    });
  }

  public async remove() {
    await deleteWorkspaceViaApi(this.request, this.config, this.id);
  }

  /**
   * Generate a new Workspace via the API
   */
  public static async create(
    context: APIRequestContext,
    config: TsetTestConfig,
    workspace: WorkspaceBuilder
  ) {
    const workspaceResponse = await createWorkspaceViaAPI(
      context,
      config,
      workspace
    );
    return new Workspace(context, config, workspaceResponse);
  }

  public static async createIfNotExists(
    context: APIRequestContext,
    config: TsetTestConfig,
    workspace: WorkspaceBuilder
  ) {
    const existingWorkspace = workspaces?.find(w => w.name === workspace.name) 
    if(!existingWorkspace) {
      return await this.create(context, config, workspace);
    }

    return new Workspace(context, config, {name: existingWorkspace.name, id: existingWorkspace.workspaceId});
  }
}
