import { FolderBuilder } from "@/api/public-api/builder/folder.builder";
import { createFolderViaAPI, removeFolderViaAPI } from "@/api/public-api/utils/folder.utils";
import { TsetTestConfig } from "@/resources/config/utils/TsetTestConfig";
import { APIRequestContext, Page } from "@playwright/test";
import { Project } from "./project";

export type FolderCreateResponseDto = {
  id: string;
  name: string;
  parentId: FolderCreateResponseDto["id"] | null;
};

export interface Folder extends FolderCreateResponseDto {}
export class Folder implements FolderCreateResponseDto {
  public id: string;
  public name: string;
  public parentId: FolderCreateResponseDto["id"] | null;

  constructor(
    private request: APIRequestContext,
    private config: TsetTestConfig,
    private page: Page,
    data: FolderCreateResponseDto
  ) {
    this.id = data.id;
    this.name = data.name;
    this.parentId = data.parentId;
  }

  public async addProject(projectName: string, projectKey: string) {
    return Project.create(this.request, this.config, this.page, {
      name: projectName,
      key: projectKey,
      folderId: this.id,
    });
  }

  public async remove() {
    removeFolderViaAPI(this.request, this.config, this.id);
  }

  public static async create(
    context: APIRequestContext,
    config: TsetTestConfig,
    page: Page,
    folder: FolderBuilder
  ) {
    const folderResponse = await createFolderViaAPI(context, config, folder);
    return new Folder(context, config, page, folderResponse);
  }
}
