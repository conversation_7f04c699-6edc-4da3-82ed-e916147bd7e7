import { ProjectBuilder } from "@/api/public-api/builder/project.builder";
import { createProjectViaAPI } from "@/api/public-api/utils/project.utils";
import { Calculation } from "@/domains/calculation/business/calculation";
import { TsetTestConfig } from "@/resources/config/utils/TsetTestConfig";
import { APIRequestContext, Page } from "@playwright/test";
import { ProjectPage } from "../pages/ProjectPage";

export type ProjectCreateResponseDto = {
  key: string;
  id: string;
  name: string;
  baseCurrency: string;
};

export interface Project extends ProjectCreateResponseDto {}
export class Project implements ProjectCreateResponseDto {
  constructor(
    private request: APIRequestContext,
    private config: TsetTestConfig,
    private page: Page,
    data: ProjectCreateResponseDto
  ) {
    this.key = data.key;
    this.id = data.id;
    this.name = data.name;
    this.baseCurrency = data.baseCurrency;
  }

  public async addCalculation() {
    return Calculation.create(this.request, this.config, this.page {
      project: this,
    });
  }

  public async goto() {
    await this.page.goto(`/project/${this.key}`);
    return new ProjectPage(this.page, this.config, this)
  }

  public static async create(
    context: APIRequestContext,
    config: TsetTestConfig,
    page: Page,
    project: ProjectBuilder
  ) {
    const projectResponse = await createProjectViaAPI(context, config, project);
    return new Project(context, config, page, projectResponse);
  }
}
