import { TsetTestConfig } from "@/resources/config/utils/TsetTestConfig";
import { Page } from "@playwright/test";
import { Project } from "../business/project";
import { AddCalculationModal } from "@/pages/calculation/modals/AddCalculation.modal";

export class ProjectPage {
  constructor(
    private page: Page,
    private config: TsetTestConfig,
    private project: Project
  ) {}

  async startToAddManualCalculation() {
    // open the context menu
    await this.page.getByRole("button", { name: "Add calculation" }).click();
    // add calculation from scratch
    await this.page.getByText("Part: from scratch").click();
    return new AddCalculationModal(this.page, this.config);
  }
}
