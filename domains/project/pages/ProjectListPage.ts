import { CalculationPage } from "@/pages/calculation/CalculationPage";
import { FolderCell } from "@/pages/project/components/ProjectFolderTable/cells/Folder.cell";
import { ProjectCell } from "@/pages/project/components/ProjectFolderTable/cells/Project.cell";
import { ProjectFolderTable } from "@/pages/project/components/ProjectFolderTable/ProjectFolderTable";
import { TsetTestConfig } from "@/resources/config/utils/TsetTestConfig";
import { Page } from "@playwright/test";

export class ProjectListPage {
  private projectFolderTable = new ProjectFolderTable(this.page, this.config);

  constructor(private page: Page, private config: TsetTestConfig) {}

  async open() {
    await this.page.goto("/project");
    return this;
  }

  async openWorkspace(workspaceName: string) {
    await this.projectFolderTable.clickWorkspaceName(workspaceName);
    return this;
  }

  async openFolder(folderName: string) {
    const nodeCell = new FolderCell(
      this.page,
      this.config,
      this.projectFolderTable.table,
      folderName
    );
    await nodeCell.clickOnChevron();
    return this;
  }

  getCalculation() {
    return new CalculationPage(this.page, this.config);
  }

  async openCalculation(projectName: string) {
    const projectRow = new ProjectCell(
      this.page,
      this.config,
      this.projectFolderTable.table,
      projectName
    );
    await projectRow.navigateToProject();

    return new CalculationPage(this.page, this.config);
  }
}
