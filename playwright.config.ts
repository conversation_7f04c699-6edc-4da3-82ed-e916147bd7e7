import { defineConfig, devices } from "@playwright/test";
import dotenv from "dotenv";
import path from "path";

dotenv.config({ path: path.resolve(".env") });

export const TestSetup = {
  defaultWorkspaceName: 'Playwright Tests'
}

// Universal timeout
const COMMON_OPERATION_TIMEOUT = 19000;

function resolveEnv(): string {
  if (undefined === process.env.ENV_NAME) {
    return "";
  }
  if ("production" === process.env.ENV_NAME) {
    return "https://cost.tset.cloud/";
  }
  if (process.env.ENV_NAME?.match(/^[a-zA-Z]+$/)) {
    return `https://cost.${process.env.ENV_NAME}.tset.cloud/`;
  }
  if (process.env.ENV_NAME?.startsWith("feature/")) {
    return `https://${process.env.ENV_NAME.split("/").at(
      1
    )}.cost.feature.tset.cloud/`;
  }
  return process.env.ENV_NAME;
}
/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  // Number of allowed total failures, set to 0 to disable
  maxFailures: 25,
  // Root folder for tests, playwright looks for tests under this folder relative to project root
  testDir: "tests",
  // Test timeout in milliseconds
  timeout: 300_000,
  // Test Run timeout in milliseconds
  globalTimeout: 7_200_000,
  /* Run tests in files in parallel */
  fullyParallel: false,
  /* I suggest you you run a single test in another way.
   * Yes this is gona be a wall of text ranting, ask and AI to read it for you!
   * This as a meaningless and dangerous feature.
   * Playwright will only know about the CI when you explicitly tell it.
   * They suggest to control this by environment variables.
   * Sure you can use the same commands to just run tests
   * but it feels so stupidly easy to shoot your self in the leg.
   * Accidentally removing the entire test run in CI. Which nobody notices as it's only a flag in a confing
   * + one line change in a see of affected files...
   * Best case someone would be wondering why do I only execute a single test as e2e regression. 0xF0
   * Works case nobody will notice untill something blows up.
   * The tests may have helped but they were not running. Because someone left this on.
   * Lights now days automatically turn off, you don't need to turn them off when you leave a room.
   * Millenials don't know they are supposed to turn of lights when they leave a room because it just happens.
   * They don't know someone needs to make it happen. (Not shaming just facts.)
   * So, no, just don't turn this on. You will have less headache. I promise! */
  forbidOnly: true,
  /* I suggest not raising this higher but
   * if you are super confident in the stability of your tests
   * you can go lower for a specific env */
  retries: 1,
  /* Effectively a number of threads */
  workers: 4,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [["html", { open: "never" }], ["allure-playwright"], ["github"]],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    // Created videos for tests. Makes sense for local run ow when running a single test.
    video: "off",
    // Take a screenshot after test. Where ever it ended.
    screenshot: "off",
    // Used by playwright to find elements with getByTestId
    testIdAttribute: "data-test",
    // Playwright will try to comunicate with this endpoint
    baseURL: `${resolveEnv()}`,
    // Timeout for playwright to interact with the elements on a page
    actionTimeout: COMMON_OPERATION_TIMEOUT,
    // Timeout for opening a page
    navigationTimeout: COMMON_OPERATION_TIMEOUT,

    /* Do not record traces for CI runs in general */
    trace: "off",
  },

  projects: [
    {
      name: "TSET authenticate before api calls",
      testDir: "tests/setup/api/",
      testMatch: "auth.spec.ts",
    },
    {
      name: "TSET Setup preferences",
      testDir: "tests/setup/api/",
      testMatch: "preferences.spec.ts",
      dependencies: ["TSET authenticate before api calls"],
    },
    {
      name: "TSET Setup prerequisits for ui tests",
      use: {
        channel: "chromium",
        viewport: null,
        launchOptions: {
          args: ["--start-maximized"],
        },
      },
      testDir: "tests/setup/ui/",
      dependencies: ["TSET Setup preferences"],
      expect: {
        // Timeout for exceptions
        timeout: COMMON_OPERATION_TIMEOUT,
      },
    },
    {
      name: "TSET Regression tests",
      use: {
        ...devices["Desktop Chrome"],
        storageState: "./playwright/.auth/session/browser/antman.json",
      },
      testDir: "tests/functional/",
      dependencies: ["TSET Setup prerequisits for ui tests"],
      expect: {
        // Timeout for exceptions
        timeout: COMMON_OPERATION_TIMEOUT,
      },
    }
  ],
});
