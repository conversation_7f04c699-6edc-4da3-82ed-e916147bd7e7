{"name": "playwright-tests", "version": "1.0.0", "main": "index.js", "license": "MIT", "devDependencies": {"@playwright/test": "^1.52.0", "@types/node": "^22.10.5", "allure-js-commons": "^3.0.8", "allure-playwright": "^3.0.8"}, "scripts": {"build": "tsc", "authapi": "pnpm exec playwright test --project 'TSET authenticate before api calls'", "setuptest": "pnpm exec playwright test --project 'TSET Setup prerequisits for ui tests'", "smoke": "pnpm exec playwright test --project 'TSET Smoke tests'", "test": "pnpm exec playwright test --project 'TSET Regression tests'"}, "dependencies": {"@slack/web-api": "^7.9.3", "dotenv": "^16.4.7"}}