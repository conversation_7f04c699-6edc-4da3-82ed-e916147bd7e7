import { Calculation } from "@/domains/calculation/business/calculation";
import { test as baseTest } from "@/fixtures/functional/fixture.base";

export const testChillCalculation = baseTest.extend<{
  calculation: Calculation;
}>({
  calculation: async ({ page, config, request, workspace }, use) => {
    // creation
    const chillCalculation = await Calculation.create(request, config, page, { workspace });
    // use
    await use(chillCalculation);
    // teardown
    await chillCalculation.remove();
  },
});
