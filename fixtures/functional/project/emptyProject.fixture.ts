import { Project } from "@/domains/project/business/project";
import { test as base } from "../fixture.base";
import { Folder } from "@/domains/project/business/folder";

export const testEmptyProject = base.extend<{
  project: Project;
}>({
  project: async ({ page, config, request, workspace }, use) => {
    // creation
    const id = String(Math.random() * 100);
    const folder = await Folder.create(request, config, page, {
      workspaceId: workspace.id,
      name: `Empty Project Folder - ${id}`,
    });
    const project = await folder.addProject(`Empty Project - ${id}`, id);
    // use
    await use(project);
    // teardown
    await folder;
  },
});