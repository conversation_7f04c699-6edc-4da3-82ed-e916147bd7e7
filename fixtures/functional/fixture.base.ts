import * as allure from "allure-js-commons";
import { test as base } from "../../fixture";
import { Workspace } from "@/domains/project/business/workspace";
import { TestSetup } from "@/playwright.config";

// export const test = base;

export const test = base.extend<{
  workspace: Workspace;
}>({
  workspace: async ({ page, config, request }, use, testinfo) => {
    // navigation
    test.step(`Open TSET url: '${
      config.playwrightConfig.projects.at(0)?.use.baseURL
    }'`, async () => {
      await page.goto("/");
    });

    // creation
    const workspace = await Workspace.createIfNotExists(request, config, {
      name: TestSetup.defaultWorkspaceName,
    });

    // use
    await use(workspace);

    // teardown
    await allure.attachment(
      `${testinfo.title} screenshot`,
      await page.screenshot({ fullPage: true }),
      allure.ContentType.PNG
    );
    await allure.attachment(
      `${testinfo.title} url`,
      page.url(),
      allure.ContentType.TEXT
    );
    await allure.attachment(
      `${testinfo.title} page title`,
      await page.title(),
      allure.ContentType.TEXT
    );
    await allure.attachment(
      `${testinfo.title} page source`,
      await page.content(),
      allure.ContentType.TEXT
    );
  },
});

export { expect } from "@playwright/test";
