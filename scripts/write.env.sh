#!/bin/bash
test -f .env || touch .env
source .env
# Make sure $ENV_NAME returns a correct value
echo $ENV_NAME
if [[ -z "${ENV_NAME}" ]]; then
  echo "ENV_NAME=$ENV_NAME";
  echo "ENV_NAME value is not set correctly, please make sure it contains the environment name you want to read the secrets for";
  exit 1;
fi;
# Values to be resolved by one password
# https://developer.1password.com/docs/cli/secrets-environment-variables/
# https://developer.1password.com/docs/cli/secret-reference-syntax/
# op://<vault-name>/<item-name>/[section-name/]<field-name> - currently we don't need to use section names
echo "TSET_USERNAME=$(op read op://Testing/QA.$ENV_NAME/username 2>/dev/null)" >> .env
echo "TSET_PASSWORD=$(op read op://Testing/QA.$ENV_NAME/password 2>/dev/null)" >> .env
echo "BCT_USERNAME=$(op read op://Testing/QA.TSET.$ENV_NAME/username 2>/dev/null)" >> .env
echo "BCT_PASSWORD=$(op read op://Testing/QA.TSET.$ENV_NAME/password 2>/dev/null)" >> .env
echo "MD_USERNAME=$(op read op://Testing/md_service_normal_user.$ENV_NAME/username 2>/dev/null)" >> .env
echo "MD_PASSWORD=$(op read op://Testing/md_service_normal_user.$ENV_NAME/password 2>/dev/null)" >> .env
echo "MD_SUPER_USERNAME=$(op read op://Testing/md_service_superuser.$ENV_NAME/username 2>/dev/null)" >> .env
echo "MD_SUPER_PASSWORD=$(op read op://Testing/md_service_superuser.$ENV_NAME/password 2>/dev/null)" >> .env
echo "MD_ADMIN_USERNAME=$(op read op://Testing/md_service_admin.$ENV_NAME/username 2>/dev/null)" >> .env
echo "MD_ADMIN_PASSWORD=$(op read op://Testing/md_service_admin.$ENV_NAME/password 2>/dev/null)" >> .env
echo "MD_EDITOR_USERNAME=$(op read op://Testing/md_service_masterdata_editor.$ENV_NAME/username 2>/dev/null)" >> .env
echo "MD_EDITOR_PASSWORD=$(op read op://Testing/md_service_masterdata_editor.$ENV_NAME/password 2>/dev/null)" >> .env
echo "MD_DATA_ADMIN_USERNAME=$(op read op://Testing/md_service_masterdata_admin.$ENV_NAME/username 2>/dev/null)" >> .env
echo "MD_DATA_ADMIN_PASSWORD=$(op read op://Testing/md_service_masterdata_admin.$ENV_NAME/password 2>/dev/null)" >> .env
echo "ROLE_USER_USERNAME=$(op read op://Testing/roleuser.$ENV_NAME/username 2>/dev/null)" >> .env
echo "ROLE_USER_PASSWORD=$(op read op://Testing/roleuser.$ENV_NAME/password 2>/dev/null)" >> .env
echo "ROLE_ADMIN_USERNAME=$(op read op://Testing/roleadmin.$ENV_NAME/username 2>/dev/null)" >> .env
echo "ROLE_ADMIN_PASSWORD=$(op read op://Testing/roleadmin.$ENV_NAME/password 2>/dev/null)" >> .env
echo "ROLE_GROUPLESS_USERNAME=$(op read op://Testing/grouplessroleuser.$ENV_NAME/username 2>/dev/null)" >> .env
echo "ROLE_GROUPLESS_PASSWORD=$(op read op://Testing/grouplessroleuser.$ENV_NAME/password 2>/dev/null)" >> .env
echo "ATTRIBUTE_USER_USERNAME=$(op read op://Testing/attributelessuser.$ENV_NAME/username 2>/dev/null)" >> .env
echo "ATTRIBUTE_USER_PASSWORD=$(op read op://Testing/attributelessuser.$ENV_NAME/password 2>/dev/null)" >> .env
echo "COM_ENGINE_USERNAME=$(op read op://Testing/commercial-engine.$ENV_NAME/username 2>/dev/null)" >> .env
echo "COM_ENGINE_PASSWORD=$(op read op://Testing/commercial-engine.$ENV_NAME/password 2>/dev/null)" >> .env
echo "KEYCLOAK_CLIENT_USER=$(op read op://Testing/keycloak.client.qa.$ENV_NAME/username 2>/dev/null)" >> .env
echo "KEYCLOAK_CLIENT_SECRET=$(op read op://Testing/keycloak.client.qa.$ENV_NAME/password 2>/dev/null)" >> .env

