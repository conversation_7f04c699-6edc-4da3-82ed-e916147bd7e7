const { WebClient } = require("@slack/web-api");

//#region SLACK CLIENT DATA
// Read a token from the environment variables
const token = process.env.SLACK_WEBHOOK_URL;

// Conversation in Slack where the message is going to be sent (Check View Channel Details -> Channel ID to get id)
const conversationId = "D03D32F38P2";
//#endregion SLACK CLIENT DATA

//#region GITLAB DATA
const gitlab_page_url = process.env.CI_JOB_URL?.replace(
  "https://git.tset.cloud/qa/",
  "https://qa.docs.tset.cloud/-/"
);
const allure_report_url =
  gitlab_page_url + "/artifacts/playwright-report/index.html";
//#endregion GITLAB DATA

// Initialize
let web: any;

if (token) {
  web = new WebClient(token);
}

//#region FUNCTIONS
async function sendResultMessage() {
  if (!web) {
    console.error("No SLACK_WEBHOOK_URL token found");
    return;
  }

  // Post a message to the channel, and await the result.
  const result = await web.chat.postMessage({
    text: "Hello world!",
    channel: conversationId,
  });
  console.log("result:", result);

  // The result contains an identifier for the message, `ts`.
  console.log(
    `Successfully send message ${result.ts} in conversation ${conversationId}`
  );
}
//#endregion FUNCTIONS

//#region SCRIPT
sendResultMessage();
//#endregion SCRIPT
