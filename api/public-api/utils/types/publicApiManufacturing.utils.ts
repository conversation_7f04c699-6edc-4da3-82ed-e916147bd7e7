import { CalculationBuilder } from "../../builder/calculation.builder";
import { FieldParameter } from "../../types/FieldParameter";
import { PublicApiManufacturing } from "../../types/PublicApiManufacturing";

export function buildPublicApiCalculation(
  calculation: CalculationBuilder
): PublicApiManufacturing {
  return {
    id: calculation.title,
    type: "MANUFACTURING",
    name: calculation.title,
    manufacturingType: calculation.technology,
    part: {
      designation: calculation.partName,
      number: calculation.partNumber,
    },
    inputs: getFieldsFromCalculation(calculation),
    overrides: [],
    outputs: [],
    children: [],
    pendingVolumeChanges: false,
    dynamicFields: [],
    isolated: false,
  };
}

function getFieldsFromCalculation(
  calculation: CalculationBuilder
): FieldParameter[] {
  const fieldList: FieldParameter[] = [];

  // Default Fields
  fieldList.push({
    name: "customProcurementType",
    value: calculation.customProcurement,
    type: "CustomProcurementType",
  });
  fieldList.push({
    name: "baseCurrency",
    value: calculation.baseCurrency,
    type: "Currency",
  });
  fieldList.push({
    name: "shapeId",
    value: calculation.shapeId,
    type: "Text",
  });
  fieldList.push({
    name: "location",
    value: calculation.location,
    type: "Text",
  });
  fieldList.push({
    name: "materialName",
    value: calculation.materialName,
    type: "Text",
  });
  fieldList.push({
    name: "netWeightPerPartForWizard",
    value: calculation.weightPerPart,
    type: "Weight",
    unit: "KILOGRAMM",
  });
  fieldList.push({
    name: "lifeTime",
    value: calculation.lifeTime,
    type: "TimeInYears",
  });
  fieldList.push({
    name: "peakUsableProductionVolumePerYear",
    value: calculation.peakValue,
    type: "Pieces",
  });
  fieldList.push({
    name: "averageUsableProductionVolumePerYear",
    value: calculation.averageValue,
    type: "Pieces",
  });
  fieldList.push({
    name: "cleaningNeeded",
    value: calculation.needsCleaning,
    type: "SelectableBoolean",
  });
  fieldList.push({
    name: "familyTooling",
    value: calculation.needsCleaning,
    type: "SelectableBoolean",
  });

  if (calculation.needsCleaning) {
    fieldList.push({
      name: "cleanness",
      value: calculation.cleannessLevel,
      type: "StepSubTypeCleanness",
    });
  }

  switch (calculation.technology) {
    case "ManufacturingChillCasting":
      fieldList.push({
        name: "fettling",
        value: calculation.needsFettling,
        type: "Bool",
      });
      fieldList.push({
        name: "hasCore",
        value: false,
        type: "SelectableBoolean",
      });
      fieldList.push({
        name: "maxWallThickness",
        value: calculation.maxWallThickness,
        type: "Length",
        unit: "MILLIMETER",
      });
      fieldList.push({
        name: "minWallThickness",
        value: calculation.minWallThickness,
        type: "Length",
        unit: "MILLIMETER",
      });
      fieldList.push({
        name: "stepSubTypeHeatTreatment",
        value: calculation.heatTreatment,
        type: "StepSubTypeHeatTreatmentAlu",
      });
      break;
    case "ManufacturingDieForgingAlu":
      break;
  }

  return fieldList;
}
