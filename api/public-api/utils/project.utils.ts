import { APIRequestContext } from "@playwright/test";
import { getAccessToken } from "../public-api.client";
import { APIRequestObject, postRequest } from "../../utils/RequestMethods";
import { ProjectBuilder } from "../builder/project.builder";
import { TsetTestConfig } from "../../../resources/config/utils/TsetTestConfig";
import { ProjectCreateResponseDto } from "@/domains/project/business/project";

export async function createProjectViaAPI(
  context: APIRequestContext,
  config: TsetTestConfig,
  project: ProjectBuilder
) {
  await getAccessToken(context, config);

  const request: APIRequestObject = {
    data: project,
  };

  const response = await postRequest(
    `Create Project ${project.name}`,
    context,
    config,
    `v1/projects`,
    request
  );

  return (await response.json()) as ProjectCreateResponseDto;
}
