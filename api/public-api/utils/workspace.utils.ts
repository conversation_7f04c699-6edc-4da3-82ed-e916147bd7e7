import { WorkspaceBuilder } from "../builder/workspace.builder";
import { getAccessToken } from "../public-api.client";
import { APIRequestContext } from "@playwright/test";
import {
  APIRequestObject,
  deleteRequest,
  getRequest,
  postRequest,
} from "../../utils/RequestMethods";
import { TsetTestConfig } from "../../../resources/config/utils/TsetTestConfig";
import { WorkspaceCreateResponseDto } from "@/domains/project/business/workspace";

export async function getAllWorkspacesViaAPI(
  context: APIRequestContext,
  config: TsetTestConfig
) {
  await getAccessToken(context, config);

  const response = await getRequest(
    `Get all Workspaces`,
    context,
    config,
    `v1/workspaces`,
  );

  return (await response.json()) as {name: string, workspaceId: string}[];
}

export async function createWorkspaceViaAPI(
  context: APIRequestContext,
  config: TsetTestConfig,
  workspace: WorkspaceBuilder
) {
  await getAccessToken(context, config);

  const request: APIRequestObject = {
    data: workspace,
  };

  const response = await postRequest(
    `Create Workspace ${workspace.name}`,
    context,
    config,
    `v1/workspaces`,
    request
  );

  return (await response.json()) as WorkspaceCreateResponseDto;
}

export async function deleteWorkspaceViaApi(
  context: APIRequestContext,
  config: TsetTestConfig,
  workspaceId: string
) {
  await getAccessToken(context, config);

  await deleteRequest(
    `Delete Workspace ${workspaceId}`,
    context,
    config,
    `v1/workspaces/${workspaceId}`
  );

  return;
}
