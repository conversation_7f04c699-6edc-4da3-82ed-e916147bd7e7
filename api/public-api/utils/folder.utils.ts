import { APIRequestContext } from "@playwright/test";
import { getAccessToken } from "../public-api.client";
import { FolderBuilder } from "../builder/folder.builder";
import { APIRequestObject, deleteRequest, postRequest } from "../../utils/RequestMethods";
import { TsetTestConfig } from "../../../resources/config/utils/TsetTestConfig";
import { FolderCreateResponseDto } from "@/domains/project/business/folder";

export async function createFolderViaAPI(
  context: APIRequestContext,
  config: TsetTestConfig,
  folder: FolderBuilder
) {
  await getAccessToken(context, config);

  const request: APIRequestObject = {
    data: folder,
  };

  const response = await postRequest(
    `Create Folder ${folder.name}`,
    context,
    config,
    `v1/workspaces/${folder.workspaceId}/folders`,
    request
  );

  return (await response.json()) as FolderCreateResponseDto;
}

export async function removeFolderViaAPI(
  context: APIRequestContext,
  config: TsetTestConfig,
  folderId: string
) {
  await getAccessToken(context, config);

  const response = await deleteRequest(
    `Delete Folder ${folderId}`,
    context,
    config,
    `v1/folders/${folderId}`,
  );

  return (await response.json()) as unknown;
}
