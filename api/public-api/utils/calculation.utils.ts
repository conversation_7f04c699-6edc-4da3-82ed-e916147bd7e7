import { APIRequestContext } from "@playwright/test";
import { CalculationBuilder } from "../builder/calculation.builder";
import { getAccessToken } from "../public-api.client";
import { PublicApiManufacturing } from "../types/PublicApiManufacturing";
import { buildPublicApiCalculation } from "./types/publicApiManufacturing.utils";
import {
  APIRequestObject,
  deleteRequest,
  getRequest,
  postRequest,
} from "../../utils/RequestMethods";
import { TsetTestConfig } from "../../../resources/config/utils/TsetTestConfig";
import { CalculationCreationResponseDto } from "@/domains/calculation/business/calculation";
import { ManufacturingDTO } from "@/domains/calculation/business/calculation.dto";

export async function createCalculationCHILLViaApi(
  context: APIRequestContext,
  config: TsetTestConfig,
  args: { projectKey: string; partNumber: string; partName: string }
) {
  const { projectKey, partNumber, partName } = args;
  await getAccessToken(context, config);

  const calculation: CalculationBuilder = {
    technology: "ManufacturingChillCasting",
    partName,
    partNumber,
    shapeId: "S_086",
    title: "Test Calculation Title",
    lifeTime: 10,
    peakValue: 150000,
    averageValue: 130000,
    location: "Austria",
    locationForUi: "Austria",
    customProcurement: "tset.ref.lov-entry.purchased",
    customProcurementName: "Purchased",
    calculationQuality: "Greenfield",
    baseCurrency: "EUR",
    weightPerPart: 0.12,
    materialName: "AlSi2MgTi-RAW_MATERIAL_CASTING_ALLOY",
    needsFettling: false,
    needsShortBlasting: false,
    heatTreatment: "T5",
    minWallThickness: 1,
    maxWallThickness: 2,
    needsCleaning: false,
  };

  const payload: PublicApiManufacturing =
    buildPublicApiCalculation(calculation);

  const request: APIRequestObject = {
    data: payload,
    headers: {
      projectKey,
    },
  };

  const response = await postRequest(
    `Create Calculation ${calculation.title}`,
    context,
    config,
    `v1/projects/${projectKey}/bomNodes`,
    request
  );

  return (await response.json()) as CalculationCreationResponseDto;
}

export async function getDetailedCalculationViaApi(
  context: APIRequestContext,
  config: TsetTestConfig,
  calculationId: string,
  branchId: string
) {
  const token = await getAccessToken(context, config);

  const response = await getRequest(
    `Get Calculation ${calculationId}`,
    context,
    config,
    `api/man/${calculationId}?branch=${branchId}`,
    {
      headers: {
        Authorization: token,
      },
    }
  );

  return (await response.json()) as {
    manufacturing: ManufacturingDTO;
  };
}

export async function deleteCalculationViaApi(
  context: APIRequestContext,
  config: TsetTestConfig,
  projectId: string,
  calculationId: string
) {
  await getAccessToken(context, config);

  await deleteRequest(
    `Delete Calculation ${calculationId}`,
    context,
    config,
    `v1/projects/${projectId}/bomNodes/${calculationId}`
  );

  return;
}
