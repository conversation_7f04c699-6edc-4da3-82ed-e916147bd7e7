import { CalculationTechnolgy } from "../../../types/calculation/calculation.data";
import { COUNTRY } from "../../../types/locations/locations.data";

export type CalculationBuilder = {
  technology: CalculationTechnolgy;
  partName: string;
  partNumber: string;
  shapeId: string;
  title: string;
  lifeTime: number;
  peakValue: number;
  averageValue: number;
  location: COUNTRY;
  locationForUi: COUNTRY;
  customProcurement: string;
  customProcurementName: string;
  calculationQuality: string;
  baseCurrency: string;
  weightPerPart: number;
  materialName: string;
  needsFettling: boolean;
  needsShortBlasting: boolean;
  heatTreatment: string;
  minWallThickness: number;
  maxWallThickness: number;
  needsCleaning: boolean;
  cleannessLevel?: string;
};
