import { APIRequestContext } from "@playwright/test";
import { APIRequestObject, postRequest } from "../utils/RequestMethods";
import { TsetTestConfig } from "../../resources/config/utils/TsetTestConfig";
import { Users } from "../../resources/config/utils/users";

export async function getAccessToken(
  request: APIRequestContext,
  config: TsetTestConfig
) {
  let username: string = config.env.getTsetUsername();
  let password: string = config.env.getTsetUserPassword();
  let client_id: string = config.env.getClientUser();
  let client_secret: string = config.env.getClientSecret();

  // Prepare the request
  let options: APIRequestObject = {
    form: {
      client_id: client_id,
      grant_type: "password",
      client_secret: client_secret,
      scope: "openid",
      username: username,
      password: password,
    },
  };

  const auth = await postRequest(
    "Call Authorization",
    request,
    config,
    config.env.getKeycloackUrl(),
    options
  );

  let access_token;

  if (auth.ok()) {
    access_token = (await auth.json()).access_token;
    Users.writeUser(username, password, access_token);
  }
  return `Bearer ${access_token}`;
}
