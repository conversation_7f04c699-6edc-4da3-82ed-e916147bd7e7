import { FieldParameter } from "./FieldParameter";

export type PublicApiManufacturing = {
  id?: string;
  type?: string;
  name: string;
  masterdataKey?: string;
  bomNodeId?: string;
  manufacturingType?: string;
  part?: {
    designation: string;
    number: string;
  };
  inputs: FieldParameter[];
  overrides: FieldParameter[];
  outputs: FieldParameter[];
  children: PublicApiManufacturing[];
  pendingVolumeChanges: boolean;
  dynamicFields: unknown[];
  isolated: boolean;
  model?: unknown;
  protectedAt?: Date;
  protectedBy?: string;
};
