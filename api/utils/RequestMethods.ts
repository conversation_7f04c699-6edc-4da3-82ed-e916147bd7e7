import * as allure from "allure-js-commons";
import { test } from "../../fixture";
import { APIRequestContext, APIResponse } from "@playwright/test";
import { Serializable } from "child_process";
import { ReadStream } from "fs";
import { TsetTestConfig } from "../../resources/config/utils/TsetTestConfig";

export type APIRequestObject = {
  data?: string | Buffer | Serializable;
  failOnStatusCode?: boolean;
  form?:
    | {
        [key: string]: string | number | boolean;
      }
    | FormData;
  headers?: {
    [key: string]: string;
  };
  ignoreHTTPSErrors?: boolean;
  maxRedirects?: number;
  maxRetries?: number;
  multipart?:
    | FormData
    | {
        [key: string]:
          | string
          | number
          | boolean
          | ReadStream
          | {
              name: string;
              mimeType: string;
              buffer: Buffer;
            };
      };
  params?:
    | {
        [key: string]: string | number | boolean;
      }
    | URLSearchParams
    | string;
  timeout?: number;
};

const addRequestToReport = async (
  requestPath: string,
  config: TsetTestConfig,
  options?: any
) => {
  let url: string = requestPath.startsWith("http")
    ? requestPath
    : config.playwrightConfig.projects.at(0)?.use.baseURL + requestPath;
  await allure.attachment("Request Url", url, allure.ContentType.TEXT);

  if (undefined !== options?.headers) {
    await allure.attachment(
      "Reques Headers",
      JSON.stringify(options.headers),
      allure.ContentType.JSON
    );
  }

  if (undefined !== options) {
    if (undefined !== options.params) {
      await allure.attachment(
        "Reques Query Params",
        JSON.stringify(options.params),
        allure.ContentType.JSON
      );
    }

    if (undefined !== options?.data) {
      let optionsDataType = typeof options.data;
      if (optionsDataType === "string") {
        await allure.attachment(
          "Reques Body Params",
          options.data,
          allure.ContentType.TEXT
        );
      } else {
        await allure.attachment(
          "Reques Body",
          JSON.stringify(optionsDataType),
          allure.ContentType.TEXT
        );
      }
    }

    if (undefined !== options?.form) {
      await allure.attachment(
        "Reques Form Params",
        JSON.stringify(options.form),
        allure.ContentType.JSON
      );
    }
  }
};

const addResponseToReport = async (response: APIResponse, limit: number) => {
  const body: string = await response.text();
  await allure.attachment(
    "Response Status Code",
    JSON.stringify(response.status()),
    allure.ContentType.TEXT
  );
  if (body.length < limit) {
    await allure.attachment("Response Body", body, allure.ContentType.TEXT);
  } else {
    await allure.attachment(
      `Response Body limited to ${limit}`,
      body.substring(0, limit),
      allure.ContentType.TEXT
    );
  }
  await allure.attachment(
    "Response url",
    response.url(),
    allure.ContentType.TEXT
  );
  await allure.attachment(
    "Response headers",
    JSON.stringify(response.headers()),
    allure.ContentType.JSON
  );
};

export const deleteRequest = async (
  stepName: string,
  context: APIRequestContext,
  config: TsetTestConfig,
  requestPath: string,
  options?: APIRequestObject
): Promise<APIResponse> => {
  return await test.step(stepName, async () => {
    await test.step("Request details", async () => {
      await addRequestToReport(requestPath, config, options);
    });
    let response = await context.delete(requestPath, options);
    await test.step("Response details", async () => {
      await addResponseToReport(response, config.responseBodyLengthLimit);
    });
    return response;
  });
};

export const getRequest = async (
  stepName: string,
  context: APIRequestContext,
  config: TsetTestConfig,
  requestPath: string,
  options?: APIRequestObject
): Promise<APIResponse> => {
  return await test.step(stepName, async () => {
    await test.step("Request details", async () => {
      await addRequestToReport(requestPath, config, options);
    });
    let response = await context.get(requestPath, options);
    await test.step("Response details", async () => {
      await addResponseToReport(response, config.responseBodyLengthLimit);
    });
    return response;
  });
};

export async function postRequest(
  stepName: string,
  context: APIRequestContext,
  config: TsetTestConfig,
  requestPath: string,
  options?: APIRequestObject
): Promise<APIResponse> {
  return await test.step(stepName, async () => {
    await test.step("Request details", async () => {
      await addRequestToReport(requestPath, config, options);
    });
    let response = await context.post(requestPath, options);
    await test.step("Response details", async () => {
      await addResponseToReport(response, config.responseBodyLengthLimit);
    });
    return response;
  });
}

export const putRequest = async (
  stepName: string,
  context: APIRequestContext,
  config: TsetTestConfig,
  requestPath: string,
  options?: APIRequestObject
): Promise<APIResponse> => {
  return await test.step(stepName, async () => {
    await test.step("Request details", async () => {
      await addRequestToReport(requestPath, config, options);
    });
    let response = await context.put(requestPath, options);
    await test.step("Response details", async () => {
      await addResponseToReport(response, config.responseBodyLengthLimit);
    });
    return response;
  });
};
