import { Locator } from "@playwright/test";
import { BaseComponent } from "../../BaseComponent";
import { test } from "../../../fixture";
import { TsetHierarchicalSelectEntry } from "./TsetHierarchicalSelectEntry";

export class TsetHierarchicalSelect extends BaseComponent {
  private entriesLocator: Locator = this.page
    .getByTestId("tset-hierarchical-select-navigator")
    .getByTestId(new RegExp(`^tset-hierarchical-select-entry`));

  public async open() {
    await this.locator.click();
  }

  public async selectEntry(entryLabel: string) {
    await test.step(`Select entry ${entryLabel}`, async () => {
      const entry = new TsetHierarchicalSelectEntry(
        this.page,
        this.config,
        this.entriesLocator.getByText(entryLabel).locator("../../..")
      );
      await entry.selectEntry();
    });
  }

  public async navigateEntry(entryLabel: string) {
    console.log(
      await this.page
        .getByTestId("tset-hierarchical-select-navigator")
        .getByTestId(/^tset-hierarchical-select-entry/)
        .getByText("Europe, Middle East and Africa")
        .locator("../../..")
        .textContent()
    );

    await test.step(`Navigate entry ${entryLabel}`, async () => {
      const entry = new TsetHierarchicalSelectEntry(
        this.page,
        this.config,
        this.entriesLocator.getByText(entryLabel).locator("../../..")
      );
      await entry.navigateEntry();
    });
  }
}
