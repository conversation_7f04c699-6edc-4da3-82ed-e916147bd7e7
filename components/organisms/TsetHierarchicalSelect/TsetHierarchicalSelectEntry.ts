import { test } from "../../../fixture";
import { TsetButton } from "../../atoms/TsetButton/TsetButton";
import { BaseComponent } from "../../BaseComponent";

export class TsetHierarchicalSelectEntry extends BaseComponent {
  private selectEntryButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.locator.getByTestId("tset-hierarchical-select-entry-select-button")
  );
  private navigateEntryButton: TsetButton = new TsetButton(
    this.page,
    this.config,
    this.locator.getByTestId("tset-hierarchical-select-entry-navigate-button")
  );

  public async selectEntry() {
    await this.selectEntryButton.click();
  }

  public async navigateEntry() {
    await this.navigateEntryButton.click();
  }
}
