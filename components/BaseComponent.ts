import { Locator, Page } from "@playwright/test";
import { BaseObject } from "./BaseObject";
import { TsetTestConfig } from "../resources/config/utils/TsetTestConfig"

export abstract class BaseComponent extends BaseObject {
  public constructor(
    page: Page,
    config: TsetTestConfig,
    protected locator: Locator
  ) {
    super(page, config);
  }

  public getLocator() {
    return this.locator;
  }
}
