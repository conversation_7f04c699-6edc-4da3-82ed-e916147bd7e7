import { expect, Locator } from "@playwright/test";
import { BaseObject } from "../../BaseObject";
import { test } from "../../../fixture";

export class TsetWave extends BaseObject {
  private locator: Locator = this.page.getByTestId("loading-wave");

  public async waitUntilLoaderIsNotPresent() {
    await test.step("Wait until loader is not present", async () => {
      await expect(this.locator.first()).not.toBeVisible();
    });
  }
}
