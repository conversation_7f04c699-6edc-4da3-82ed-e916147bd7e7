import { expect, Locator } from "@playwright/test";
import { BaseComponent } from "../../BaseComponent";
import { test } from "../../../fixture";

export class NuClassSelector extends BaseComponent {
  private selectorStep: Locator = this.locator.getByTestId(
    "nu-class-selector-step"
  );

  public async selectItem(value: string) {
    await test.step(`Select ${value} from NuClassSelector`, async () => {
      await this.locator.click();

      await this.selectorStep
        .getByTestId("nu-class-selector-step-item")
        .getByText(value)
        .click();
      await expect(this.selectorStep).not.toBeVisible();
    });
  }
}
