import { Locator } from "@playwright/test";
import { BaseComponent } from "../../BaseComponent";

export class TsetTable extends BaseComponent {
  public getTableHeader(): Locator {
    return this.locator.locator('[class="header-table"]');
  }

  public getTableBody(): Locator {
    return this.locator.getByRole("grid");
  }

  public getTableFooter(): Locator {
    return this.locator.getByTestId("table-footer");
  }
}
