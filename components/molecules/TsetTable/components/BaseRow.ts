import { Locator, Page } from "@playwright/test";
import { TsetTestConfig } from "../../../../utils/config/TsetTestConfig";
import { BaseObject } from "../../../BaseObject";
import { TsetTable } from "../TsetTable";

export class BaseCell extends BaseObject {
  protected mainLocator: Locator;
  protected table: TsetTable;

  public constructor(
    page: Page,
    config: TsetTestConfig,
    table: TsetTable,
    cellTextContent: string
  ) {
    super(page, config);
    this.table = table;
    this.mainLocator = this.table.getLocator().getByRole("gridcell", {
      name: cellTextContent,
      exact: true,
    });
  }

  public async click() {
    await this.mainLocator.click();
  }

  public async clickOnChevron() {
    await this.mainLocator.getByRole("img").first().click();
  }
}
