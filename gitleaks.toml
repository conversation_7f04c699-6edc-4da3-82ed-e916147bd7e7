title = "Custom Gitleaks configuration
# Read the related docs for details on how to use this gitleaks.toml file to inject custom configuration.
# https://github.com/gitleaks/gitleaks?tab=readme-ov-file#configuration

[extend]
# <useDefault> will extend the default gitleaks config built in to the binary.
# The latest version is located at:
# https://github.com/gitleaks/gitleaks/blob/master/config/gitleaks.toml

useDefault = true
